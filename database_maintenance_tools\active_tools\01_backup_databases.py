#!/usr/bin/env python3
"""
数据库备份脚本
在进行架构优化前，先备份现有数据库
"""

import sqlite3
import shutil
import json
import os
from datetime import datetime
from pathlib import Path

class DatabaseBackup:
    """数据库备份工具"""
    
    def __init__(self):
        self.backup_dir = Path("database_backups")
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_info = {
            'timestamp': self.timestamp,
            'backups': [],
            'verification': {}
        }
    
    def create_backup(self):
        """创建数据库备份"""
        print("🔄 开始数据库备份...")
        print("=" * 60)
        
        # 1. 创建备份目录
        self._create_backup_directory()
        
        # 2. 备份数据库文件
        self._backup_database_files()
        
        # 3. 导出数据为SQL
        self._export_sql_dumps()
        
        # 4. 验证备份完整性
        self._verify_backups()
        
        # 5. 生成备份报告
        self._generate_backup_report()
        
        print(f"\n✅ 备份完成！备份目录: {self.backup_dir / self.timestamp}")
        return True
    
    def _create_backup_directory(self):
        """创建备份目录"""
        backup_path = self.backup_dir / self.timestamp
        backup_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建备份目录: {backup_path}")
    
    def _backup_database_files(self):
        """备份数据库文件"""
        print(f"\n📋 备份数据库文件...")
        
        databases = ["resource.db", "fin_data.db"]
        backup_path = self.backup_dir / self.timestamp
        
        for db_file in databases:
            if Path(db_file).exists():
                backup_file = backup_path / f"{db_file}.backup"
                shutil.copy2(db_file, backup_file)
                
                # 记录备份信息
                file_info = {
                    'original_file': db_file,
                    'backup_file': str(backup_file),
                    'original_size': Path(db_file).stat().st_size,
                    'backup_size': backup_file.stat().st_size,
                    'backup_time': datetime.now().isoformat()
                }
                self.backup_info['backups'].append(file_info)
                
                print(f"  ✅ {db_file} → {backup_file}")
                print(f"     大小: {file_info['original_size']:,} bytes")
            else:
                print(f"  ❌ 文件不存在: {db_file}")
    
    def _export_sql_dumps(self):
        """导出SQL转储文件"""
        print(f"\n📋 导出SQL转储文件...")
        
        databases = ["resource.db", "fin_data.db"]
        backup_path = self.backup_dir / self.timestamp
        
        for db_file in databases:
            if Path(db_file).exists():
                sql_file = backup_path / f"{db_file}.sql"
                
                try:
                    # 连接数据库
                    conn = sqlite3.connect(db_file)
                    
                    # 导出SQL
                    with open(sql_file, 'w', encoding='utf-8') as f:
                        for line in conn.iterdump():
                            f.write(f"{line}\n")
                    
                    conn.close()
                    
                    print(f"  ✅ {db_file} → {sql_file}")
                    print(f"     大小: {sql_file.stat().st_size:,} bytes")
                    
                except Exception as e:
                    print(f"  ❌ 导出失败 {db_file}: {e}")
    
    def _verify_backups(self):
        """验证备份完整性"""
        print(f"\n🔍 验证备份完整性...")
        
        for backup_info in self.backup_info['backups']:
            original_file = backup_info['original_file']
            backup_file = Path(backup_info['backup_file'])
            
            # 验证文件大小
            size_match = backup_info['original_size'] == backup_info['backup_size']
            
            # 验证表数量
            table_count_match = self._verify_table_count(original_file, backup_file)
            
            # 验证数据行数
            row_count_match = self._verify_row_count(original_file, backup_file)
            
            verification_result = {
                'file': original_file,
                'size_match': size_match,
                'table_count_match': table_count_match,
                'row_count_match': row_count_match,
                'overall_valid': size_match and table_count_match and row_count_match
            }
            
            self.backup_info['verification'][original_file] = verification_result
            
            status = "✅" if verification_result['overall_valid'] else "❌"
            print(f"  {status} {original_file}")
            print(f"     文件大小: {'✅' if size_match else '❌'}")
            print(f"     表数量: {'✅' if table_count_match else '❌'}")
            print(f"     数据行数: {'✅' if row_count_match else '❌'}")
    
    def _verify_table_count(self, original_file, backup_file):
        """验证表数量"""
        try:
            # 原文件表数量
            conn1 = sqlite3.connect(original_file)
            cursor1 = conn1.cursor()
            cursor1.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            original_count = cursor1.fetchone()[0]
            conn1.close()
            
            # 备份文件表数量
            conn2 = sqlite3.connect(backup_file)
            cursor2 = conn2.cursor()
            cursor2.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            backup_count = cursor2.fetchone()[0]
            conn2.close()
            
            return original_count == backup_count
            
        except Exception as e:
            print(f"    ⚠️ 表数量验证失败: {e}")
            return False
    
    def _verify_row_count(self, original_file, backup_file):
        """验证数据行数"""
        try:
            # 获取原文件总行数
            original_total = self._get_total_row_count(original_file)
            
            # 获取备份文件总行数
            backup_total = self._get_total_row_count(backup_file)
            
            return original_total == backup_total
            
        except Exception as e:
            print(f"    ⚠️ 行数验证失败: {e}")
            return False
    
    def _get_total_row_count(self, db_file):
        """获取数据库总行数"""
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in cursor.fetchall()]
        
        total_rows = 0
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            table_rows = cursor.fetchone()[0]
            total_rows += table_rows
        
        conn.close()
        return total_rows
    
    def _generate_backup_report(self):
        """生成备份报告"""
        backup_path = self.backup_dir / self.timestamp
        report_file = backup_path / "backup_report.json"
        
        # 添加汇总信息
        self.backup_info['summary'] = {
            'total_files_backed_up': len(self.backup_info['backups']),
            'all_verifications_passed': all(
                v['overall_valid'] for v in self.backup_info['verification'].values()
            ),
            'backup_directory': str(backup_path),
            'backup_completed_at': datetime.now().isoformat()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.backup_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 备份报告已保存: {report_file}")
        
        # 打印汇总
        print(f"\n📊 备份汇总:")
        print(f"  - 备份文件数: {self.backup_info['summary']['total_files_backed_up']}")
        print(f"  - 验证通过: {'✅' if self.backup_info['summary']['all_verifications_passed'] else '❌'}")
        print(f"  - 备份目录: {backup_path}")

def main():
    """主函数"""
    print("🔄 数据库备份工具")
    print(f"📅 备份时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    backup_tool = DatabaseBackup()
    success = backup_tool.create_backup()
    
    if success:
        print(f"\n🎉 备份成功完成！")
        print(f"💡 提示: 请妥善保管备份文件，在架构优化过程中如遇问题可用于恢复")
    else:
        print(f"\n❌ 备份过程中出现错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
