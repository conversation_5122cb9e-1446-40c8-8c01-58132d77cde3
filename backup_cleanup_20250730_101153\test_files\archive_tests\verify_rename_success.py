#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def verify_column_rename_success(db_path="fin_data.db"):
    """验证列名重命名操作是否成功"""
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 验证 financial_data 表列名重命名结果...")
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        
        # 预期的英文列名
        expected_columns = [
            'year', 'month', 'accounting_organization', 'accounting_unit_name',
            'account_code', 'account_full_name', 'account_name', 'opening_debit_amount',
            'opening_credit_amount', 'account_direction', 'project_id', 'project_code',
            'project_name', 'market_nature_id', 'tax_rate_id', 'tax_rate_name',
            'business_format_id', 'financial_product_id', 'long_term_deferred_project_id',
            'property_unit_id', 'cash_flow_project_id', 'municipal_enterprise_unit_id',
            'bank_account_id', 'financial_institution_id', 'bank_routing_number',
            'bank_name', 'debit_amount', 'debit_cumulative', 'credit_amount',
            'credit_cumulative', 'balance'
        ]
        
        # 获取实际列名
        actual_columns = [col[1] for col in columns]
        
        print(f"📊 表结构验证:")
        print(f"   预期列数: {len(expected_columns)}")
        print(f"   实际列数: {len(actual_columns)}")
        
        # 检查列名是否匹配
        missing_columns = set(expected_columns) - set(actual_columns)
        extra_columns = set(actual_columns) - set(expected_columns)
        
        success = True
        
        if missing_columns:
            print(f"❌ 缺少列: {missing_columns}")
            success = False
        
        if extra_columns:
            print(f"⚠️ 额外列: {extra_columns}")
        
        if len(actual_columns) != len(expected_columns):
            print(f"❌ 列数不匹配")
            success = False
        
        # 检查是否还有中文列名
        chinese_columns = []
        for col_name in actual_columns:
            # 检查是否包含中文字符
            if any('\u4e00' <= char <= '\u9fff' for char in col_name):
                chinese_columns.append(col_name)
        
        if chinese_columns:
            print(f"❌ 仍存在中文列名: {chinese_columns}")
            success = False
        
        # 数据完整性检查
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        row_count = cursor.fetchone()[0]
        print(f"📈 数据完整性:")
        print(f"   总行数: {row_count:,}")
        
        if row_count == 0:
            print("❌ 数据丢失 - 表为空")
            success = False
        
        # 测试查询新列名
        try:
            cursor.execute("""
                SELECT year, month, account_name, debit_amount, credit_amount 
                FROM financial_data 
                LIMIT 3
            """)
            sample_data = cursor.fetchall()
            print(f"✅ 新列名查询测试成功")
            print("   示例数据:")
            for i, row in enumerate(sample_data, 1):
                print(f"     行{i}: 年={row[0]}, 月={row[1]}, 科目={row[2]}, 借方={row[3]}, 贷方={row[4]}")
        except Exception as e:
            print(f"❌ 新列名查询测试失败: {str(e)}")
            success = False
        
        conn.close()
        
        # 最终结果
        if success:
            print("\n🎉 验证成功!")
            print("✅ 所有列名已成功重命名为英文")
            print("✅ 数据完整性良好")
            print("✅ 新列名查询正常")
            return True
        else:
            print("\n❌ 验证失败!")
            print("请检查上述错误信息")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程中出错: {str(e)}")
        return False

def show_column_comparison():
    """显示重命名前后的列名对比"""
    
    # 原中文列名
    old_columns = [
        '﻿年', '月', '核算组织', '核算单位名称', '科目编号', '科目全称', '科目名称',
        '期初借方金额', '期初贷方金额', '科目方向', '项目ID', '项目编号', '项目名称',
        '市场性质ID', '税率ID', '税率名称', '业态ID', '金融产品ID', '长期待摊项目ID',
        '楼盘房号ID', '现金流量项目ID', '市属国企单位ID', '银行账号ID', '金融机构ID',
        '联行号', '银行名称', '借方金额', '借方累计', '贷方金额', '贷方累计', '余额'
    ]
    
    # 新英文列名
    new_columns = [
        'year', 'month', 'accounting_organization', 'accounting_unit_name',
        'account_code', 'account_full_name', 'account_name', 'opening_debit_amount',
        'opening_credit_amount', 'account_direction', 'project_id', 'project_code',
        'project_name', 'market_nature_id', 'tax_rate_id', 'tax_rate_name',
        'business_format_id', 'financial_product_id', 'long_term_deferred_project_id',
        'property_unit_id', 'cash_flow_project_id', 'municipal_enterprise_unit_id',
        'bank_account_id', 'financial_institution_id', 'bank_routing_number',
        'bank_name', 'debit_amount', 'debit_cumulative', 'credit_amount',
        'credit_cumulative', 'balance'
    ]
    
    print("\n📋 列名重命名对比表:")
    print("=" * 80)
    print(f"{'序号':<4} {'原中文列名':<20} {'新英文列名':<35}")
    print("-" * 80)
    
    for i, (old, new) in enumerate(zip(old_columns, new_columns), 1):
        print(f"{i:<4} {old:<20} {new:<35}")

if __name__ == "__main__":
    print("🚀 开始验证 financial_data 表列名重命名结果...")
    
    # 显示对比表
    show_column_comparison()
    
    # 执行验证
    success = verify_column_rename_success()
    
    if success:
        print("\n✨ 恭喜！列名重命名操作完全成功！")
        print("📝 详细报告请查看: FINANCIAL_DATA_COLUMN_RENAME_REPORT.md")
    else:
        print("\n⚠️ 验证发现问题，请检查并修复。")
        print("🔄 如需回滚，请使用备份文件恢复。")
