# Docker 环境配置指南

本项目提供了多种Docker配置方案，适用于不同的开发和部署场景。

## 📁 文件结构

```
chatdb/
├── docker-compose.yml          # 完整应用栈（生产环境）
├── docker-compose.dev.yml      # 开发环境基础服务
└── backend/
    └── docker-compose.redis.yml # Redis单独服务（已废弃）
```

## 🚀 使用场景

### 1. 开发环境（推荐）

**适用场景**：本地开发，需要数据库和缓存服务，但应用在本地运行以便调试

```bash
# 启动开发环境基础服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

**包含服务**：
- MySQL (端口: 3306)
- Neo4j (端口: 7474, 7687)
- Redis (端口: 6379)

**本地运行应用**：
```bash
# 后端
cd backend
python main.py

# 前端
cd frontend
npm start
```

### 2. 完整应用栈（生产环境）

**适用场景**：完整部署，所有服务都在容器中运行

```bash
# 启动完整应用栈
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend

# 停止服务
docker-compose down
```

**包含服务**：
- MySQL (端口: 3306)
- Neo4j (端口: 7474, 7687)
- Redis (端口: 6379)
- Backend (端口: 8000)
- Frontend (端口: 3000)

### 3. 仅Redis服务（已废弃）

~~`backend/docker-compose.redis.yml` 文件已被整合到主配置中，不再推荐单独使用。~~

## 🔧 环境变量配置

### 开发环境
确保 `backend/.env` 文件包含以下配置：

```env
# 数据库配置
DATABASE_TYPE=mysql  # 或 sqlite
MYSQL_SERVER=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DB=chatdb

# Neo4j配置
NEO4J_URI=neo4j://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=Di@nhua11

# Redis配置
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### 生产环境
Docker Compose会自动设置容器间的网络连接，无需修改配置。

## 📊 服务健康检查

### 检查服务状态
```bash
# MySQL
docker exec chatdb-mysql-dev mysql -u root -ppassword -e "SELECT 1"

# Neo4j
docker exec chatdb-neo4j-dev cypher-shell -u neo4j -p Di@nhua11 "RETURN 1"

# Redis
docker exec chatdb-redis-dev redis-cli ping
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs redis
```

## 🗂️ 数据持久化

所有数据都存储在Docker volumes中：

- `mysql_dev_data`: MySQL数据
- `neo4j_dev_data`: Neo4j数据
- `neo4j_dev_logs`: Neo4j日志
- `redis_dev_data`: Redis数据

### 清理数据
```bash
# 停止服务并删除volumes（谨慎操作！）
docker-compose -f docker-compose.dev.yml down -v

# 仅删除特定volume
docker volume rm chatdb_redis_dev_data
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | findstr :3306
   netstat -an | findstr :6379
   netstat -an | findstr :7687
   ```

2. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.dev.yml logs [service_name]
   
   # 重新构建容器
   docker-compose -f docker-compose.dev.yml up --build
   ```

3. **数据连接问题**
   ```bash
   # 测试网络连接
   docker exec chatdb-backend-dev ping chatdb-mysql-dev
   ```

## 📝 最佳实践

1. **开发时使用 `docker-compose.dev.yml`**
2. **生产部署使用 `docker-compose.yml`**
3. **定期备份重要数据volumes**
4. **使用 `.env` 文件管理敏感配置**
5. **监控容器资源使用情况**
