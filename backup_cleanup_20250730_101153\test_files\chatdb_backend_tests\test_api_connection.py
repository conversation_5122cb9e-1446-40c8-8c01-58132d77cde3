#!/usr/bin/env python3
"""
API连接测试脚本
用于测试DeepSeek API的连接状态和配置
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.core.llms import _setup_model_client, get_model_client
import requests
import json

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_api_key_format():
    """测试API密钥格式"""
    api_key = settings.OPENAI_API_KEY
    logger.info(f"API密钥: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
    
    if not api_key:
        logger.error("❌ API密钥为空")
        return False
    
    if not api_key.startswith('sk-'):
        logger.warning("⚠️ API密钥格式可能不正确，应该以'sk-'开头")
    
    logger.info("✅ API密钥格式检查通过")
    return True


def test_network_connectivity():
    """测试网络连接"""
    try:
        logger.info("测试网络连接...")
        response = requests.get("https://www.baidu.com", timeout=10)
        if response.status_code == 200:
            logger.info("✅ 网络连接正常")
            return True
        else:
            logger.error(f"❌ 网络连接异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 网络连接失败: {str(e)}")
        return False


def test_aliyun_api_connectivity():
    """测试阿里云百炼API连接"""
    try:
        logger.info("测试阿里云百炼API连接...")

        headers = {
            "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }

        # 测试简单的API调用
        data = {
            "model": settings.LLM_MODEL,
            "messages": [
                {"role": "user", "content": "你好"}
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }

        response = requests.post(
            f"{settings.OPENAI_API_BASE}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        logger.info(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            logger.info("✅ 阿里云百炼API连接成功")
            logger.info(f"响应内容: {result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')}")
            return True
        elif response.status_code == 401:
            logger.error("❌ API密钥无效或已过期")
            return False
        elif response.status_code == 429:
            logger.error("❌ API调用频率限制")
            return False
        else:
            logger.error(f"❌ API调用失败: {response.status_code}")
            logger.error(f"错误详情: {response.text}")
            return False

    except requests.exceptions.Timeout:
        logger.error("❌ API请求超时")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("❌ 无法连接到阿里云百炼API服务器")
        return False
    except Exception as e:
        logger.error(f"❌ API测试失败: {str(e)}")
        return False


async def test_autogen_client():
    """测试AutoGen客户端"""
    try:
        logger.info("测试AutoGen模型客户端...")
        
        # 尝试初始化客户端
        client = _setup_model_client()
        logger.info("✅ AutoGen客户端初始化成功")
        
        # 这里可以添加更多的客户端测试
        return True
        
    except Exception as e:
        logger.error(f"❌ AutoGen客户端测试失败: {str(e)}")
        return False


def print_configuration():
    """打印当前配置"""
    logger.info("=== 当前配置 ===")
    logger.info(f"API Base: {settings.OPENAI_API_BASE}")
    logger.info(f"模型: {settings.LLM_MODEL}")
    logger.info(f"温度: {settings.LLM_TEMPERATURE}")
    logger.info(f"最大Token: {settings.LLM_MAX_TOKENS}")
    logger.info("================")


def main():
    """主测试函数"""
    logger.info("🔍 开始API连接诊断...")
    
    print_configuration()
    
    # 测试步骤
    tests = [
        ("API密钥格式", test_api_key_format),
        ("网络连接", test_network_connectivity),
        ("阿里云百炼API连接", test_aliyun_api_connectivity),
        ("AutoGen客户端", lambda: asyncio.run(test_autogen_client())),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- 测试: {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n=== 测试结果汇总 ===")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试通过，API配置正常！")
    else:
        logger.error("⚠️ 部分测试失败，请检查配置和网络连接")
        
        # 提供解决建议
        logger.info("\n=== 解决建议 ===")
        logger.info("1. 检查API密钥是否有效")
        logger.info("2. 确认网络连接正常")
        logger.info("3. 验证DeepSeek API服务状态")
        logger.info("4. 检查防火墙和代理设置")


if __name__ == "__main__":
    main()
