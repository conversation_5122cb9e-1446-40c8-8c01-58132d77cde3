{"timestamp": "20250729_235700", "files_with_references": [{"file_path": "chatdb\\backend\\test_db_connection.py", "references": [{"table_name": "table_descriptions", "line_number": 28, "line_content": "metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 28, "line_content": "metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 28, "line_content": "metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "chatdb\\backend\\app\\models\\metadata_models.py", "references": [{"table_name": "column_descriptions", "line_number": 70, "line_content": "__tablename__ = \"column_descriptions\"", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 70, "line_content": "__tablename__ = \"column_descriptions\"", "pattern_type": "__tablename__\\s*=\\s*[\"\\'](column_descriptions)[\"\\']"}]}, {"file_path": "chatdb\\backend\\app\\services\\enhanced_prompt_service.py", "references": [{"table_name": "column_descriptions", "line_number": 47, "line_content": "'column_descriptions': [],", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 65, "line_content": "metadata['column_descriptions'].append({", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 193, "line_content": "for meta_col in enhanced_metadata.get('column_descriptions', []):", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 59, "line_content": "FROM column_descriptions", "pattern_type": "FROM\\s+(column_descriptions)\\b"}, {"table_name": "business_rules", "line_number": 48, "line_content": "'business_rules': [],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 86, "line_content": "metadata['business_rules'].append({", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 80, "line_content": "FROM business_rules", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "chatdb\\backend\\app\\services\\text2sql_service.py", "references": [{"table_name": "column_descriptions", "line_number": 136, "line_content": "if metadata.get(\"column_descriptions\"):", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 141, "line_content": "for col in metadata[\"column_descriptions\"]:", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 125, "line_content": "if metadata.get(\"business_rules\"):", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 127, "line_content": "for rule in metadata[\"business_rules\"]:", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 279, "line_content": "\"business_rules_applied\": len(metadata.get(\"business_rules\", [])) if metadata else 0,", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "chatdb\\backend\\app\\services\\text2sql_utils.py", "references": [{"table_name": "table_descriptions", "line_number": 665, "line_content": "cursor.execute(\"SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'\")", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "table_descriptions", "line_number": 674, "line_content": "FROM table_descriptions", "pattern_type": "FROM\\s+(table_descriptions)\\b"}, {"table_name": "column_descriptions", "line_number": 659, "line_content": "\"column_descriptions\": [],", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 696, "line_content": "metadata[\"column_descriptions\"].append({", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 730, "line_content": "logger.info(f\"成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则\")", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 754, "line_content": "column_meta_map = {col[\"column_name\"]: col for col in metadata.get(\"column_descriptions\", [])}", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 690, "line_content": "FROM column_descriptions", "pattern_type": "FROM\\s+(column_descriptions)\\b"}, {"table_name": "business_rules", "line_number": 660, "line_content": "\"business_rules\": [],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 701, "line_content": "\"business_rules\": col[4],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 720, "line_content": "metadata[\"business_rules\"].append({", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 730, "line_content": "logger.info(f\"成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则\")", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 762, "line_content": "column[\"business_rules\"] = meta_col[\"business_rules\"]", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 762, "line_content": "column[\"business_rules\"] = meta_col[\"business_rules\"]", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 708, "line_content": "FROM business_rules", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "02_unify_table_structures.py", "references": [{"table_name": "column_descriptions", "line_number": 89, "line_content": "'table': 'column_descriptions',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 198, "line_content": "verification_results['column_descriptions'] = self._verify_column_descriptions_structure()", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 161, "line_content": "'table': 'business_rules',", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 201, "line_content": "verification_results['business_rules'] = self._verify_business_rules_data()", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 123, "line_content": "business_cursor.execute(\"SELECT * FROM business_rules\")", "pattern_type": "FROM\\s+(business_rules)\\b"}, {"table_name": "business_rules", "line_number": 133, "line_content": "resource_cursor.execute(\"SELECT table_name, rule_category, rule_description FROM business_rules\")", "pattern_type": "FROM\\s+(business_rules)\\b"}, {"table_name": "business_rules", "line_number": 266, "line_content": "resource_cursor.execute(\"SELECT COUNT(*) FROM business_rules\")", "pattern_type": "FROM\\s+(business_rules)\\b"}, {"table_name": "business_rules", "line_number": 269, "line_content": "business_cursor.execute(\"SELECT COUNT(*) FROM business_rules\")", "pattern_type": "FROM\\s+(business_rules)\\b"}, {"table_name": "business_rules", "line_number": 151, "line_content": "insert_sql = f\"INSERT INTO business_rules ({','.join(columns)}) VALUES ({placeholders})\"", "pattern_type": "INSERT\\s+INTO\\s+(business_rules)\\b"}]}, {"file_path": "03_rename_duplicate_tables.py", "references": [{"table_name": "table_descriptions", "line_number": 26, "line_content": "'table_descriptions': 'meta_table_descriptions',", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "table_descriptions", "line_number": 32, "line_content": "'table_descriptions': 'biz_table_descriptions',", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 27, "line_content": "'column_descriptions': 'meta_column_descriptions',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 33, "line_content": "'column_descriptions': 'biz_column_descriptions',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 28, "line_content": "'business_rules': 'meta_business_rules'", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 34, "line_content": "'business_rules': 'biz_business_rules'", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "05_simple_code_analysis.py", "references": [{"table_name": "table_descriptions", "line_number": 20, "line_content": "self.old_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "table_descriptions", "line_number": 24, "line_content": "'table_descriptions': {", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 20, "line_content": "self.old_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 28, "line_content": "'column_descriptions': {", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 20, "line_content": "self.old_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 32, "line_content": "'business_rules': {", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "analyze_database_tables.py", "references": [{"table_name": "table_descriptions", "line_number": 44, "line_content": "elif table_name in ['table_descriptions', 'column_descriptions', 'business_rules']:", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "table_descriptions", "line_number": 99, "line_content": "if table_name in ['table_descriptions', 'business_rules', 'column_descriptions'] and count < 50:", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "table_descriptions", "line_number": 114, "line_content": "'table_descriptions': '📊 表元数据：存储数据库表的描述信息，为AI提供表级别的语义理解',", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 44, "line_content": "elif table_name in ['table_descriptions', 'column_descriptions', 'business_rules']:", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 99, "line_content": "if table_name in ['table_descriptions', 'business_rules', 'column_descriptions'] and count < 50:", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 115, "line_content": "'column_descriptions': '📋 字段元数据：存储每个字段的详细描述和AI理解要点，支持智能SQL生成',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 44, "line_content": "elif table_name in ['table_descriptions', 'column_descriptions', 'business_rules']:", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 99, "line_content": "if table_name in ['table_descriptions', 'business_rules', 'column_descriptions'] and count < 50:", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 116, "line_content": "'business_rules': '⚖️ 业务规则：存储财务业务规则，确保AI生成的SQL符合财务逻辑',", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "analyze_tables_simple.py", "references": [{"table_name": "table_descriptions", "line_number": 35, "line_content": "'table_descriptions': {", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 39, "line_content": "'column_descriptions': {", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 43, "line_content": "'business_rules': {", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "enhanced_prompt_service.py", "references": [{"table_name": "column_descriptions", "line_number": 44, "line_content": "'column_descriptions': [],", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 62, "line_content": "metadata['column_descriptions'].append({", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 190, "line_content": "for meta_col in enhanced_metadata.get('column_descriptions', []):", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 56, "line_content": "FROM column_descriptions", "pattern_type": "FROM\\s+(column_descriptions)\\b"}, {"table_name": "business_rules", "line_number": 45, "line_content": "'business_rules': [],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 83, "line_content": "metadata['business_rules'].append({", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 77, "line_content": "FROM business_rules", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "enhance_metadata_system.py", "references": [{"table_name": "column_descriptions", "line_number": 155, "line_content": "UPDATE column_descriptions", "pattern_type": "UPDATE\\s+(column_descriptions)\\b"}]}, {"file_path": "final_compatibility_verification.py", "references": [{"table_name": "column_descriptions", "line_number": 122, "line_content": "'column_descriptions',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 143, "line_content": "if 'column_descriptions' in existing_tables:", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 123, "line_content": "'business_rules',", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "fix_compatibility_issues.py", "references": [{"table_name": "column_descriptions", "line_number": 131, "line_content": "__tablename__ = \"column_descriptions\"", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 131, "line_content": "__tablename__ = \"column_descriptions\"", "pattern_type": "__tablename__\\s*=\\s*[\"\\'](column_descriptions)[\"\\']"}]}, {"file_path": "integration_guide.py", "references": [{"table_name": "column_descriptions", "line_number": 50, "line_content": "\"column_descriptions\": [],", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 68, "line_content": "metadata[\"column_descriptions\"].append({", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 200, "line_content": "op.add_column('column_descriptions', sa.Column('field_category', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 201, "line_content": "op.add_column('column_descriptions', sa.Column('usage_scenarios', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 202, "line_content": "op.add_column('column_descriptions', sa.Column('common_values', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 203, "line_content": "op.add_column('column_descriptions', sa.Column('related_fields', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 204, "line_content": "op.add_column('column_descriptions', sa.Column('calculation_rules', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 205, "line_content": "op.add_column('column_descriptions', sa.Column('ai_prompt_hints', sa.Text(), nullable=True))", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 225, "line_content": "op.drop_column('column_descriptions', 'ai_prompt_hints')", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 250, "line_content": "assert 'column_descriptions' in metadata", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 253, "line_content": "assert len(metadata['column_descriptions']) > 0", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 62, "line_content": "FROM column_descriptions", "pattern_type": "FROM\\s+(column_descriptions)\\b"}, {"table_name": "business_rules", "line_number": 51, "line_content": "\"business_rules\": [],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 78, "line_content": "\"business_rules\": row[9]", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 90, "line_content": "metadata[\"business_rules\"].append({", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 251, "line_content": "assert 'business_rules' in metadata", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 84, "line_content": "FROM business_rules", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "simple_integration_test.py", "references": [{"table_name": "column_descriptions", "line_number": 36, "line_content": "'column_descriptions',", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 80, "line_content": "'column_descriptions': [],", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 95, "line_content": "metadata['column_descriptions'].append({", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 151, "line_content": "for col in metadata['column_descriptions'][:3]:", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 167, "line_content": "print(f\"  📊 字段描述: {len(metadata['column_descriptions'])} 个\")", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 89, "line_content": "FROM column_descriptions", "pattern_type": "FROM\\s+(column_descriptions)\\b"}, {"table_name": "business_rules", "line_number": 37, "line_content": "'business_rules',", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 81, "line_content": "'business_rules': [],", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 113, "line_content": "metadata['business_rules'].append({", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 157, "line_content": "for rule in metadata['business_rules'][:2]:", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 168, "line_content": "print(f\"  ⚖️ 业务规则: {len(metadata['business_rules'])} 个\")", "pattern_type": "[\"\\'](business_rules)[\"\\']"}, {"table_name": "business_rules", "line_number": 107, "line_content": "FROM business_rules", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "test_integration.py", "references": [{"table_name": "column_descriptions", "line_number": 50, "line_content": "print(f\"  📊 字段描述数量: {len(metadata.get('column_descriptions', []))}\")", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 51, "line_content": "print(f\"  ⚖️ 业务规则数量: {len(metadata.get('business_rules', []))}\")", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "test_optimization_results.py", "references": [{"table_name": "business_rules", "line_number": 46, "line_content": "cursor.execute(\"SELECT COUNT(*) FROM business_rules\")", "pattern_type": "FROM\\s+(business_rules)\\b"}, {"table_name": "business_rules", "line_number": 50, "line_content": "cursor.execute(\"SELECT COUNT(*) FROM business_rules WHERE importance_level = 'CRITICAL'\")", "pattern_type": "FROM\\s+(business_rules)\\b"}]}, {"file_path": "系统修复脚本.py", "references": [{"table_name": "table_descriptions", "line_number": 411, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 411, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 411, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}, {"file_path": "系统诊断脚本.py", "references": [{"table_name": "table_descriptions", "line_number": 195, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](table_descriptions)[\"\\']"}, {"table_name": "column_descriptions", "line_number": 195, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](column_descriptions)[\"\\']"}, {"table_name": "business_rules", "line_number": 195, "line_content": "required_tables = ['table_descriptions', 'column_descriptions', 'business_rules']", "pattern_type": "[\"\\'](business_rules)[\"\\']"}]}], "summary": {"total_files_with_references": 20, "total_references": 116, "table_statistics": {"table_descriptions": {"files": 8, "references": 13}, "column_descriptions": {"files": 19, "references": 54}, "business_rules": {"files": 17, "references": 49}}, "analysis_completed_at": "2025-07-29T23:57:00.928994"}}