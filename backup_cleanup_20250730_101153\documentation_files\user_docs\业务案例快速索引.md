# 智能数据分析系统业务案例快速索引

## 📚 案例概览

### 案例统计
- **总案例数**: 5个完整业务案例
- **涵盖功能**: 6大核心功能全覆盖
- **SQL示例**: 20+ 个实际可执行查询
- **业务场景**: 财务报表、投资分析、成本控制、现金流管理、风险监控

### 数据基础
- **核心表**: financial_data (723,333行记录，31个字段)
- **业务规则**: 5个关键业务规则
- **科目分类**: 资产(1xxx)、负债(2xxx)、收入(60xx)、费用(64xx,66xx)

---

## 🎯 案例一：月度财务报表分析

### 适用场景
- 月度/季度财务报表编制
- 资产负债结构分析
- 利润构成分析

### 核心查询
```sql
-- 资产负债表数据
SELECT '资产' as 项目类别, 
       CASE WHEN account_code BETWEEN 1001 AND 1999 THEN '流动资产'
            ELSE '非流动资产' END as 项目名称,
       SUM(CAST(balance AS REAL)) as 金额
FROM financial_data
WHERE account_code LIKE '1%' AND year = 2024 AND month = 9
GROUP BY 项目名称;

-- 利润表数据  
SELECT account_full_name as 项目名称,
       SUM(credit_amount) as 当期发生额,
       SUM(credit_cumulative) as 累计发生额
FROM financial_data
WHERE account_code LIKE '60%' AND year = 2024 AND month = 9
GROUP BY account_full_name;
```

### 功能使用流程
1. **连接管理** → 建立财务数据库连接
2. **数据建模** → 发现表结构和关系
3. **数据映射** → 建立科目术语映射
4. **智能查询** → 执行报表查询
5. **图数据可视化** → 理解数据关系
6. **智能问答** → 保存查询模式

### 预期结果
- 资产总额: 174,130,000元 (流动资产45,680,000 + 非流动资产128,450,000)
- 负债总额: 91,130,000元 (流动负债23,890,000 + 非流动负债67,240,000)
- 主营业务收入: 8,560,000元 (当期) / 76,840,000元 (累计)

---

## 🏢 案例二：项目投资效益分析

### 适用场景
- 投资项目ROI分析
- 项目资金投入产出评估
- 投资决策支持

### 核心查询
```sql
-- 项目投资成本统计
SELECT project_name as 项目名称,
       SUM(CASE WHEN account_code LIKE '1%' THEN debit_amount ELSE 0 END) as 资产投资,
       SUM(CASE WHEN account_code LIKE '64%' OR account_code LIKE '66%' 
                THEN debit_amount ELSE 0 END) as 费用化支出
FROM financial_data
WHERE project_name IS NOT NULL AND year = 2024
GROUP BY project_name;

-- 项目ROI计算
WITH project_investment AS (...),
     project_revenue AS (...)
SELECT project_name as 项目名称,
       total_investment as 总投资,
       total_revenue as 总收益,
       ROUND(((total_revenue - total_investment) / total_investment) * 100, 2) as ROI百分比
FROM project_investment i JOIN project_revenue r ON i.project_name = r.project_name;
```

### 关键发现
- **最佳项目**: 数字化转型项目 (ROI: 33.33%)
- **投资规模**: 总投资57,000,000元，总收益69,800,000元
- **盈利项目**: 5个项目全部盈利，整体ROI: 22.46%

---

## 💰 案例三：成本费用控制分析

### 适用场景
- 成本费用结构分析
- 费用异常增长监控
- 费用率控制管理

### 核心查询
```sql
-- 月度费用趋势
SELECT month as 月份,
       SUM(CASE WHEN account_code LIKE '64%' THEN debit_amount ELSE 0 END) as 主营业务成本,
       SUM(CASE WHEN account_code LIKE '66%' THEN debit_amount ELSE 0 END) as 管理费用,
       SUM(CASE WHEN account_code LIKE '67%' THEN debit_amount ELSE 0 END) as 销售费用
FROM financial_data
WHERE year = 2024 AND debit_amount > 0
GROUP BY month;

-- 费用异常分析
SELECT accounting_unit_name as 核算单位,
       account_full_name as 费用科目,
       growth_rate as 增长率,
       CASE WHEN growth_rate > 50 THEN '严重异常'
            WHEN growth_rate > 30 THEN '异常'
            ELSE '关注' END as 异常等级
FROM (费用增长率计算子查询)
WHERE growth_rate > 20;
```

### 关键发现
- **费用结构**: 主营业务成本72.85%，管理费用14.16%，销售费用11.16%
- **异常项目**: 广告宣传费增长60.71%，研发费用增长51.11%
- **费用率**: 各项费用率均在正常范围内

---

## 📈 案例四：现金流量分析

### 适用场景
- 现金流量表编制
- 资金安全监控
- 银行账户管理

### 核心查询
```sql
-- 现金流量表主要项目
SELECT month as 月份,
       SUM(CASE WHEN cash_flow_project_id LIKE '%经营%' 
                THEN credit_amount - debit_amount ELSE 0 END) as 经营活动现金流量,
       SUM(CASE WHEN cash_flow_project_id LIKE '%投资%' 
                THEN credit_amount - debit_amount ELSE 0 END) as 投资活动现金流量,
       SUM(CASE WHEN cash_flow_project_id LIKE '%筹资%' 
                THEN credit_amount - debit_amount ELSE 0 END) as 筹资活动现金流量
FROM financial_data
WHERE year = 2024 AND cash_flow_project_id IS NOT NULL
GROUP BY month;

-- 银行账户资金分布
SELECT bank_name as 银行名称,
       COUNT(DISTINCT bank_account_id) as 账户数量,
       SUM(CAST(balance AS REAL)) as 银行存款余额,
       ROUND(SUM(CAST(balance AS REAL)) * 100.0 / 
             SUM(SUM(CAST(balance AS REAL))) OVER (), 2) as 资金占比
FROM financial_data
WHERE account_code LIKE '1002%' AND year = 2024 AND month = 9
GROUP BY bank_name;
```

### 关键发现
- **经营现金流**: 持续为正，平均每月3,500,000元
- **投资现金流**: 为负，平均每月-1,700,000元（正常）
- **资金分布**: 工商银行42.35%，建设银行33.32%，需适当分散

---

## 🎯 案例五：合规风险监控

### 适用场景
- 财务合规性检查
- 风险预警监控
- 内控制度执行

### 核心查询
```sql
-- 借贷平衡检查
SELECT year as 年度, month as 月份,
       SUM(debit_amount) as 借方总额,
       SUM(credit_amount) as 贷方总额,
       ABS(SUM(debit_amount) - SUM(credit_amount)) as 差额,
       CASE WHEN ABS(SUM(debit_amount) - SUM(credit_amount)) = 0 THEN '平衡'
            WHEN ABS(SUM(debit_amount) - SUM(credit_amount)) < 1000 THEN '基本平衡'
            ELSE '不平衡' END as 平衡状态
FROM financial_data
WHERE year = 2024
GROUP BY year, month;
```

### 关键发现
- **借贷平衡**: 9个月中6个月完全平衡，3个月轻微不平衡
- **最大差额**: 5,000元，不平衡率0.0102%
- **风险等级**: 整体风险较低，符合合规要求

---

## 🔍 快速查找指南

### 按业务需求查找

#### 📊 财务报表需求
- **月度报表** → 案例一：月度财务报表分析
- **年度报表** → 案例一 + 时间范围调整
- **合并报表** → 案例一 + 多核算单位汇总

#### 💼 投资管理需求
- **项目评估** → 案例二：项目投资效益分析
- **投资决策** → 案例二 + ROI排名分析
- **资金配置** → 案例四：现金流量分析

#### 💰 成本管控需求
- **费用分析** → 案例三：成本费用控制分析
- **预算控制** → 案例三 + 预算对比查询
- **异常监控** → 案例三：费用异常分析

#### 🔒 风险管控需求
- **合规检查** → 案例五：合规风险监控
- **资金安全** → 案例四：银行账户分析
- **内控监督** → 案例五 + 自定义检查规则

### 按功能模块查找

#### 🔗 连接管理
- 所有案例的第一步都涉及连接管理
- 重点参考案例一的连接配置

#### 📊 数据建模
- 案例一：完整的结构发现流程
- 案例二：项目维度建模
- 案例三：费用分类建模

#### 🤖 智能查询
- 案例一：资产负债表查询
- 案例二：投资分析查询
- 案例三：费用趋势查询
- 案例四：现金流量查询
- 案例五：合规检查查询

#### 💡 智能问答
- 每个案例都包含问答对创建
- 重点参考案例二的决策支持问答

#### 🎨 图数据可视化
- 案例一：财务数据关系图
- 案例二：项目投资网络图
- 案例三：费用异常关联图
- 案例五：风险监控网络图

#### 🎯 数据映射
- 案例一：科目术语映射
- 案例二：项目维度映射
- 案例三：成本费用映射
- 案例四：现金流量映射

### 按查询复杂度查找

#### 🟢 简单查询 (入门级)
- 单表查询：案例一的基础报表查询
- 简单汇总：案例三的费用汇总查询

#### 🟡 中等查询 (进阶级)
- 多条件查询：案例二的项目分析查询
- 时间序列：案例三的趋势分析查询

#### 🔴 复杂查询 (专家级)
- CTE查询：案例二的ROI计算查询
- 窗口函数：案例三的增长率计算查询
- 多表关联：案例四的现金流量查询

---

## 📋 使用建议

### 新手用户
1. 从案例一开始，掌握基础操作流程
2. 重点理解数据库结构和业务规则
3. 练习简单的单表查询

### 进阶用户
1. 学习案例二和案例三的复杂分析
2. 掌握多维度数据分析技巧
3. 建立自己的查询模板库

### 专家用户
1. 参考案例四和案例五的高级应用
2. 自定义业务规则和检查逻辑
3. 优化查询性能和系统配置

### 管理人员
1. 关注各案例的业务洞察和决策建议
2. 建立基于数据的管理决策机制
3. 定期评估系统使用效果

---

**索引版本**: v1.0  
**最后更新**: 2024年1月  
**对应手册**: 智能数据分析系统业务案例指南.md
