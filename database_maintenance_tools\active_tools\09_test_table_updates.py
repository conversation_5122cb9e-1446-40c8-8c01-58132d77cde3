#!/usr/bin/env python3
"""
测试表名更新脚本
验证数据库架构优化和代码更新是否成功
"""

import sqlite3
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目路径到sys.path
sys.path.append(str(Path(__file__).parent / "chatdb" / "backend"))

class TableUpdateTester:
    """表名更新测试器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_results = {
            'database_tests': {},
            'code_tests': {},
            'overall_success': False
        }
    
    def run_tests(self):
        """运行所有测试"""
        print("🧪 开始测试表名更新...")
        print("=" * 50)
        
        try:
            # 1. 测试数据库表结构
            self._test_database_structure()
            
            # 2. 测试代码导入
            self._test_code_imports()
            
            # 3. 测试数据库连接
            self._test_database_connections()
            
            # 4. 生成测试报告
            self._generate_test_report()
            
            return self.test_results['overall_success']
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
            return False
    
    def _test_database_structure(self):
        """测试数据库表结构"""
        print(f"\n📊 测试数据库表结构...")
        
        # 测试resource.db
        resource_result = self._test_single_database('resource.db', 'meta')
        self.test_results['database_tests']['resource_db'] = resource_result
        
        # 测试fin_data.db
        business_result = self._test_single_database('fin_data.db', 'biz')
        self.test_results['database_tests']['business_db'] = business_result
        
        # 检查是否还有重复表名
        duplicate_check = self._check_no_duplicates()
        self.test_results['database_tests']['no_duplicates'] = duplicate_check
    
    def _test_single_database(self, db_file, prefix):
        """测试单个数据库"""
        if not Path(db_file).exists():
            print(f"  ❌ 数据库文件不存在: {db_file}")
            return {'success': False, 'error': f'文件不存在: {db_file}'}
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 检查新表名是否存在
            expected_tables = [
                f'{prefix}_table_descriptions',
                f'{prefix}_column_descriptions',
                f'{prefix}_business_rules'
            ]
            
            found_tables = []
            missing_tables = []
            
            for expected_table in expected_tables:
                if expected_table in tables:
                    found_tables.append(expected_table)
                    print(f"  ✅ 找到表: {expected_table}")
                else:
                    missing_tables.append(expected_table)
                    print(f"  ❌ 缺少表: {expected_table}")
            
            # 检查旧表名是否还存在
            old_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
            remaining_old_tables = [table for table in old_tables if table in tables]
            
            if remaining_old_tables:
                print(f"  ⚠️ 仍有旧表名: {remaining_old_tables}")
            
            conn.close()
            
            result = {
                'success': len(missing_tables) == 0 and len(remaining_old_tables) == 0,
                'found_tables': found_tables,
                'missing_tables': missing_tables,
                'remaining_old_tables': remaining_old_tables,
                'total_tables': len(tables)
            }
            
            return result
            
        except Exception as e:
            print(f"  ❌ 测试数据库失败 {db_file}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _check_no_duplicates(self):
        """检查是否还有重复表名"""
        print(f"\n🔍 检查重复表名...")
        
        try:
            # 获取两个数据库的表名
            resource_tables = self._get_table_names('resource.db')
            business_tables = self._get_table_names('fin_data.db')
            
            # 检查重复
            duplicates = resource_tables & business_tables
            
            if duplicates:
                print(f"  ❌ 仍有重复表名: {duplicates}")
                return {'success': False, 'duplicates': list(duplicates)}
            else:
                print(f"  ✅ 无重复表名")
                return {'success': True, 'duplicates': []}
        
        except Exception as e:
            print(f"  ❌ 检查重复表名失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_table_names(self, db_file):
        """获取数据库表名"""
        if not Path(db_file).exists():
            return set()
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = set(row[0] for row in cursor.fetchall())
        conn.close()
        return tables
    
    def _test_code_imports(self):
        """测试代码导入"""
        print(f"\n📦 测试代码导入...")
        
        # 测试核心模块导入
        test_modules = [
            'app.models.metadata_models',
            'app.services.text2sql_utils',
            'app.services.text2sql_service'
        ]
        
        import_results = {}
        
        for module_name in test_modules:
            try:
                __import__(module_name)
                print(f"  ✅ 导入成功: {module_name}")
                import_results[module_name] = {'success': True}
            except Exception as e:
                print(f"  ❌ 导入失败: {module_name} - {e}")
                import_results[module_name] = {'success': False, 'error': str(e)}
        
        self.test_results['code_tests']['imports'] = import_results
    
    def _test_database_connections(self):
        """测试数据库连接"""
        print(f"\n🔗 测试数据库连接...")
        
        # 测试基本连接
        connection_results = {}
        
        for db_file in ['resource.db', 'fin_data.db']:
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 执行简单查询
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                
                conn.close()
                
                print(f"  ✅ 连接成功: {db_file} ({table_count}个表)")
                connection_results[db_file] = {
                    'success': True,
                    'table_count': table_count
                }
                
            except Exception as e:
                print(f"  ❌ 连接失败: {db_file} - {e}")
                connection_results[db_file] = {
                    'success': False,
                    'error': str(e)
                }
        
        self.test_results['code_tests']['connections'] = connection_results
    
    def _generate_test_report(self):
        """生成测试报告"""
        print(f"\n📊 生成测试报告...")
        
        # 计算总体成功率
        db_success = all(
            result.get('success', False) 
            for result in self.test_results['database_tests'].values()
        )
        
        import_success = all(
            result.get('success', False)
            for result in self.test_results['code_tests'].get('imports', {}).values()
        )
        
        connection_success = all(
            result.get('success', False)
            for result in self.test_results['code_tests'].get('connections', {}).values()
        )
        
        self.test_results['overall_success'] = db_success and import_success and connection_success
        
        # 打印汇总
        print(f"\n📋 测试结果汇总:")
        print(f"  - 数据库结构: {'✅' if db_success else '❌'}")
        print(f"  - 代码导入: {'✅' if import_success else '❌'}")
        print(f"  - 数据库连接: {'✅' if connection_success else '❌'}")
        print(f"  - 整体状态: {'✅ 成功' if self.test_results['overall_success'] else '❌ 失败'}")
        
        # 详细结果
        if not self.test_results['overall_success']:
            print(f"\n⚠️ 详细错误信息:")
            
            # 数据库错误
            for db_name, result in self.test_results['database_tests'].items():
                if not result.get('success', False):
                    print(f"  - {db_name}: {result.get('error', '未知错误')}")
            
            # 导入错误
            for module_name, result in self.test_results['code_tests'].get('imports', {}).items():
                if not result.get('success', False):
                    print(f"  - {module_name}: {result.get('error', '未知错误')}")

def main():
    """主函数"""
    print("🧪 表名更新测试工具")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = TableUpdateTester()
    success = tester.run_tests()
    
    if success:
        print(f"\n🎉 所有测试通过！表名更新成功！")
        print(f"💡 提示: 数据库架构优化已完成，系统可以正常使用")
    else:
        print(f"\n❌ 测试失败，请检查错误信息")
        print(f"💡 提示: 如需回滚，请运行 python 08_quick_rollback.py")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
