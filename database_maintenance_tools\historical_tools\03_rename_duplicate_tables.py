#!/usr/bin/env python3
"""
重命名重复表脚本
解决表名冲突问题，使用前缀区分元数据和业务数据
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path

class TableRenamer:
    """表重命名工具"""
    
    def __init__(self):
        self.resource_db = "resource.db"
        self.business_db = "fin_data.db"
        self.rename_log = {
            'timestamp': datetime.now().isoformat(),
            'renames': [],
            'errors': []
        }
        
        # 重命名映射
        self.resource_renames = {
            'table_descriptions': 'meta_table_descriptions',
            'column_descriptions': 'meta_column_descriptions',
            'business_rules': 'meta_business_rules'
        }
        
        self.business_renames = {
            'table_descriptions': 'biz_table_descriptions',
            'column_descriptions': 'biz_column_descriptions',
            'business_rules': 'biz_business_rules'
        }
    
    def rename_tables(self):
        """重命名重复表"""
        print("🔄 开始重命名重复表...")
        print("=" * 60)
        
        try:
            # 1. 重命名resource.db中的表
            self._rename_resource_tables()
            
            # 2. 重命名fin_data.db中的表
            self._rename_business_tables()
            
            # 3. 验证重命名结果
            self._verify_renames()
            
            # 4. 生成重命名报告
            self._generate_rename_report()
            
            print(f"\n✅ 表重命名完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 重命名过程中出现错误: {e}")
            self.rename_log['errors'].append({
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def _rename_resource_tables(self):
        """重命名resource.db中的表"""
        print(f"\n📋 重命名 {self.resource_db} 中的表...")
        
        conn = sqlite3.connect(self.resource_db)
        cursor = conn.cursor()
        
        try:
            for old_name, new_name in self.resource_renames.items():
                try:
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (old_name,))
                    if cursor.fetchone():
                        # 重命名表
                        rename_sql = f"ALTER TABLE {old_name} RENAME TO {new_name}"
                        cursor.execute(rename_sql)
                        
                        print(f"  ✅ {old_name} → {new_name}")
                        
                        self.rename_log['renames'].append({
                            'database': self.resource_db,
                            'old_name': old_name,
                            'new_name': new_name,
                            'sql': rename_sql,
                            'timestamp': datetime.now().isoformat()
                        })
                    else:
                        print(f"  ⚠️ 表不存在: {old_name}")
                        
                except Exception as e:
                    print(f"  ❌ 重命名失败 {old_name}: {e}")
                    self.rename_log['errors'].append({
                        'error': f"重命名失败 {old_name}: {e}",
                        'database': self.resource_db,
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 提交更改
            conn.commit()
            
        finally:
            conn.close()
        
        print(f"  ✅ {self.resource_db} 表重命名完成")
    
    def _rename_business_tables(self):
        """重命名fin_data.db中的表"""
        print(f"\n📋 重命名 {self.business_db} 中的表...")
        
        conn = sqlite3.connect(self.business_db)
        cursor = conn.cursor()
        
        try:
            for old_name, new_name in self.business_renames.items():
                try:
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (old_name,))
                    if cursor.fetchone():
                        # 重命名表
                        rename_sql = f"ALTER TABLE {old_name} RENAME TO {new_name}"
                        cursor.execute(rename_sql)
                        
                        print(f"  ✅ {old_name} → {new_name}")
                        
                        self.rename_log['renames'].append({
                            'database': self.business_db,
                            'old_name': old_name,
                            'new_name': new_name,
                            'sql': rename_sql,
                            'timestamp': datetime.now().isoformat()
                        })
                    else:
                        print(f"  ⚠️ 表不存在: {old_name}")
                        
                except Exception as e:
                    print(f"  ❌ 重命名失败 {old_name}: {e}")
                    self.rename_log['errors'].append({
                        'error': f"重命名失败 {old_name}: {e}",
                        'database': self.business_db,
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 提交更改
            conn.commit()
            
        finally:
            conn.close()
        
        print(f"  ✅ {self.business_db} 表重命名完成")
    
    def _verify_renames(self):
        """验证重命名结果"""
        print(f"\n🔍 验证重命名结果...")
        
        verification_results = {}
        
        # 验证resource.db
        verification_results['resource_db'] = self._verify_database_renames(
            self.resource_db, self.resource_renames
        )
        
        # 验证business.db
        verification_results['business_db'] = self._verify_database_renames(
            self.business_db, self.business_renames
        )
        
        # 检查是否还有重复表名
        verification_results['no_duplicates'] = self._verify_no_duplicates()
        
        # 打印验证结果
        for db, result in verification_results.items():
            if isinstance(result, dict):
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {db}: {result['message']}")
            else:
                status = "✅" if result else "❌"
                print(f"  {status} {db}")
        
        self.rename_log['verification'] = verification_results
        
        return all(
            result['success'] if isinstance(result, dict) else result 
            for result in verification_results.values()
        )
    
    def _verify_database_renames(self, db_file, rename_mapping):
        """验证单个数据库的重命名结果"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = set(row[0] for row in cursor.fetchall())
            
            conn.close()
            
            # 检查旧表名是否还存在
            old_tables_remaining = set(rename_mapping.keys()) & tables
            
            # 检查新表名是否存在
            new_tables_present = set(rename_mapping.values()) & tables
            
            if old_tables_remaining:
                return {
                    'success': False,
                    'message': f'仍有旧表名存在: {old_tables_remaining}',
                    'old_tables_remaining': list(old_tables_remaining),
                    'new_tables_present': list(new_tables_present)
                }
            elif len(new_tables_present) != len(rename_mapping):
                return {
                    'success': False,
                    'message': f'新表名不完整: 期望{len(rename_mapping)}个，实际{len(new_tables_present)}个',
                    'expected_new_tables': list(rename_mapping.values()),
                    'actual_new_tables': list(new_tables_present)
                }
            else:
                return {
                    'success': True,
                    'message': f'重命名成功，{len(new_tables_present)}个表已重命名',
                    'renamed_tables': list(new_tables_present)
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'验证失败: {e}'
            }
    
    def _verify_no_duplicates(self):
        """验证不再有重复表名"""
        try:
            # 获取两个数据库的所有表名
            resource_tables = self._get_table_names(self.resource_db)
            business_tables = self._get_table_names(self.business_db)
            
            # 检查是否有重复
            duplicates = resource_tables & business_tables
            
            if duplicates:
                print(f"    ❌ 仍有重复表名: {duplicates}")
                return False
            else:
                print(f"    ✅ 无重复表名")
                return True
                
        except Exception as e:
            print(f"    ❌ 验证重复表名失败: {e}")
            return False
    
    def _get_table_names(self, db_file):
        """获取数据库中的所有表名"""
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = set(row[0] for row in cursor.fetchall())
        
        conn.close()
        return tables
    
    def _generate_rename_report(self):
        """生成重命名报告"""
        report_file = f"table_rename_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加汇总信息
        self.rename_log['summary'] = {
            'total_renames': len(self.rename_log['renames']),
            'total_errors': len(self.rename_log['errors']),
            'success': len(self.rename_log['errors']) == 0,
            'completed_at': datetime.now().isoformat(),
            'resource_renames': self.resource_renames,
            'business_renames': self.business_renames
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.rename_log, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 重命名报告已保存: {report_file}")
        
        # 打印汇总
        print(f"\n📊 重命名汇总:")
        print(f"  - 执行重命名: {self.rename_log['summary']['total_renames']} 个表")
        print(f"  - 错误数量: {self.rename_log['summary']['total_errors']} 项")
        print(f"  - 整体状态: {'✅ 成功' if self.rename_log['summary']['success'] else '❌ 有错误'}")
        
        # 打印重命名映射
        print(f"\n📋 重命名映射:")
        print(f"  resource.db:")
        for old, new in self.resource_renames.items():
            print(f"    {old} → {new}")
        print(f"  fin_data.db:")
        for old, new in self.business_renames.items():
            print(f"    {old} → {new}")

def main():
    """主函数"""
    print("🔄 表重命名工具")
    print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    renamer = TableRenamer()
    success = renamer.rename_tables()
    
    if success:
        print(f"\n🎉 表重命名成功完成！")
        print(f"💡 提示: 现在需要更新应用代码中的表名引用")
        print(f"💡 建议: 运行数据库结构分析脚本验证结果")
    else:
        print(f"\n❌ 表重命名过程中出现错误，请检查日志")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
