#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def test_metadata_queries():
    """测试元数据查询功能"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧪 测试元数据查询功能")
        print("=" * 60)
        
        # 测试1: 查询表描述
        print("\n1️⃣ 测试表描述查询:")
        cursor.execute('''
            SELECT table_name, description, data_scale 
            FROM table_descriptions 
            WHERE table_name = 'financial_data'
        ''')
        result = cursor.fetchone()
        if result:
            print(f"✅ 表名: {result[0]}")
            print(f"✅ 描述: {result[1][:50]}...")
            print(f"✅ 数据规模: {result[2]}")
        else:
            print("❌ 未找到表描述")
        
        # 测试2: 查询字段数量
        print("\n2️⃣ 测试字段描述查询:")
        cursor.execute('''
            SELECT COUNT(*) FROM column_descriptions 
            WHERE table_name = 'financial_data'
        ''')
        count = cursor.fetchone()[0]
        print(f"✅ 字段描述数量: {count}")
        
        # 测试3: 查询核心金额字段
        print("\n3️⃣ 测试核心金额字段查询:")
        cursor.execute('''
            SELECT column_name, chinese_name 
            FROM column_descriptions 
            WHERE table_name = 'financial_data' 
                AND (column_name LIKE '%amount%' OR column_name = 'balance')
            ORDER BY column_name
        ''')
        results = cursor.fetchall()
        for row in results:
            print(f"✅ {row[0]} ({row[1]})")
        
        # 测试4: 查询业务规则
        print("\n4️⃣ 测试业务规则查询:")
        cursor.execute('''
            SELECT rule_category, importance_level 
            FROM business_rules 
            WHERE table_name = 'financial_data'
            ORDER BY importance_level DESC
        ''')
        results = cursor.fetchall()
        for row in results:
            print(f"✅ {row[0]} - {row[1]}")
        
        # 测试5: 测试元数据视图
        print("\n5️⃣ 测试元数据视图:")
        cursor.execute('SELECT table_name, current_record_count FROM financial_data_with_metadata')
        result = cursor.fetchone()
        if result:
            print(f"✅ 表名: {result[0]}")
            print(f"✅ 当前记录数: {result[1]:,}")
        else:
            print("❌ 元数据视图查询失败")
        
        # 测试6: 测试字段元数据视图
        print("\n6️⃣ 测试字段元数据视图:")
        cursor.execute('SELECT COUNT(*) FROM financial_data_columns_metadata')
        count = cursor.fetchone()[0]
        print(f"✅ 字段元数据视图记录数: {count}")
        
        # 测试7: 测试实际业务查询（基于业务规则）
        print("\n7️⃣ 测试实际业务查询:")
        
        # 测试资产类科目查询（使用balance字段）
        cursor.execute('''
            SELECT COUNT(*) 
            FROM financial_data 
            WHERE account_code LIKE '1%' AND balance IS NOT NULL
        ''')
        asset_count = cursor.fetchone()[0]
        print(f"✅ 资产类科目记录数: {asset_count:,}")
        
        # 测试收入类科目查询（使用credit_amount字段）
        cursor.execute('''
            SELECT COUNT(*) 
            FROM financial_data 
            WHERE account_code LIKE '60%' AND credit_amount > 0
        ''')
        revenue_count = cursor.fetchone()[0]
        print(f"✅ 有收入发生的记录数: {revenue_count:,}")
        
        # 测试费用类科目查询（使用debit_amount字段）
        cursor.execute('''
            SELECT COUNT(*) 
            FROM financial_data 
            WHERE (account_code LIKE '64%' OR account_code LIKE '66%') 
                AND debit_amount > 0
        ''')
        expense_count = cursor.fetchone()[0]
        print(f"✅ 有费用发生的记录数: {expense_count:,}")
        
        # 测试8: 验证数据类型转换
        print("\n8️⃣ 测试数据类型转换:")
        cursor.execute('''
            SELECT COUNT(*) 
            FROM financial_data 
            WHERE balance IS NOT NULL 
                AND balance != '' 
                AND CAST(balance AS REAL) IS NOT NULL
        ''')
        valid_balance_count = cursor.fetchone()[0]
        print(f"✅ 可转换为数值的余额记录数: {valid_balance_count:,}")
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！元数据系统工作正常。")
        
        conn.close()
        
    except Exception as e:
        print(f'测试时出错: {e}')

def show_usage_examples():
    """显示使用示例"""
    
    print("\n📚 元数据使用示例")
    print("=" * 60)
    
    examples = [
        {
            "title": "查询表基本信息",
            "sql": "SELECT * FROM table_descriptions WHERE table_name = 'financial_data';"
        },
        {
            "title": "查询所有字段的中文名",
            "sql": "SELECT column_name, chinese_name FROM column_descriptions WHERE table_name = 'financial_data';"
        },
        {
            "title": "查询金额字段的使用规则",
            "sql": """SELECT column_name, chinese_name, ai_understanding_points 
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND (column_name LIKE '%amount%' OR column_name = 'balance');"""
        },
        {
            "title": "查询关键业务规则",
            "sql": """SELECT rule_description, sql_example 
FROM business_rules 
WHERE table_name = 'financial_data' 
    AND importance_level = 'CRITICAL';"""
        },
        {
            "title": "正确的资产查询（使用balance字段）",
            "sql": """SELECT accounting_unit_name, SUM(CAST(balance AS REAL)) as total_assets
FROM financial_data
WHERE account_code LIKE '1%' AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;"""
        },
        {
            "title": "正确的收入查询（使用credit_amount字段）",
            "sql": """SELECT accounting_unit_name, SUM(credit_amount) as total_revenue
FROM financial_data
WHERE account_code LIKE '60%' AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;"""
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"```sql\n{example['sql']}\n```")

if __name__ == '__main__':
    test_metadata_queries()
    show_usage_examples()
