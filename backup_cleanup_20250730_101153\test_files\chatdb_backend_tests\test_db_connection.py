#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import sqlite3
import os
import sys

def test_database_connection():
    """测试数据库连接和基本功能"""
    print("🔍 开始数据库连接测试...")
    
    # 测试 resource.db
    resource_db_path = "../../resource.db"
    if os.path.exists(resource_db_path):
        try:
            conn = sqlite3.connect(resource_db_path)
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"✅ resource.db 连接成功，包含 {len(tables)} 个表:")
            for table in tables:
                print(f"   - {table[0]}")
            
            # 检查元数据表
            metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
            for table in metadata_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   📊 {table}: {count} 条记录")
                except sqlite3.OperationalError:
                    print(f"   ❌ {table}: 表不存在")
            
            conn.close()
        except Exception as e:
            print(f"❌ resource.db 连接失败: {e}")
    else:
        print(f"❌ resource.db 文件不存在: {resource_db_path}")
    
    # 测试 fin_data.db
    fin_data_path = "../../fin_data.db"
    if os.path.exists(fin_data_path):
        try:
            conn = sqlite3.connect(fin_data_path)
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"✅ fin_data.db 连接成功，包含 {len(tables)} 个表:")
            for table in tables:
                print(f"   - {table[0]}")
            
            # 检查 financial_data 表
            try:
                cursor.execute("SELECT COUNT(*) FROM financial_data")
                count = cursor.fetchone()[0]
                print(f"   📊 financial_data: {count} 条记录")
                
                # 检查表结构
                cursor.execute("PRAGMA table_info(financial_data)")
                columns = cursor.fetchall()
                print(f"   📋 financial_data 字段 ({len(columns)} 个):")
                for col in columns[:5]:  # 只显示前5个字段
                    print(f"      - {col[1]} ({col[2]})")
                if len(columns) > 5:
                    print(f"      ... 还有 {len(columns) - 5} 个字段")
                    
            except sqlite3.OperationalError as e:
                print(f"   ❌ financial_data 表查询失败: {e}")
            
            conn.close()
        except Exception as e:
            print(f"❌ fin_data.db 连接失败: {e}")
    else:
        print(f"❌ fin_data.db 文件不存在: {fin_data_path}")
    
    print("🏁 数据库连接测试完成")

if __name__ == "__main__":
    test_database_connection()
