#!/usr/bin/env python3
"""
Text2SQL API功能测试脚本
"""
import requests
import json
import time

def test_text2sql_api():
    """测试Text2SQL API功能"""
    print("🔍 开始Text2SQL API功能测试...")
    
    base_url = "http://localhost:8000"
    
    # 1. 测试API根路径
    try:
        response = requests.get(f"{base_url}/api/", timeout=10)
        if response.status_code == 200:
            api_info = response.json()
            print("✅ API根路径访问成功")
            print(f"   版本: {api_info.get('version')}")
            print(f"   状态: {api_info.get('status')}")
        else:
            print(f"❌ API根路径访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API根路径访问异常: {e}")

    # 2. 测试API文档
    try:
        response = requests.get(f"{base_url}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API文档可访问")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")

    # 3. 测试数据库连接列表
    try:
        response = requests.get(f"{base_url}/api/connections/", timeout=10)
        if response.status_code == 200:
            connections = response.json()
            print(f"✅ 数据库连接列表获取成功，共 {len(connections)} 个连接")
            for conn in connections:
                print(f"   - ID: {conn.get('id')}, 名称: {conn.get('name')}, 类型: {conn.get('db_type')}")
        else:
            print(f"❌ 数据库连接列表获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 数据库连接列表获取异常: {e}")
    
    # 4. 测试Text2SQL查询
    test_queries = [
        "查询2024年9月的收入情况",
        "显示2024年9月的资产余额",
        "统计2024年的费用支出"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: {query}")
        try:
            payload = {
                "natural_language_query": query,
                "connection_id": 1  # 假设第一个连接ID为1
            }
            
            response = requests.post(
                f"{base_url}/api/query/",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 查询成功")
                sql = result.get('sql', 'N/A')
                if sql and sql != 'N/A':
                    print(f"   SQL: {sql[:100]}...")
                else:
                    print(f"   SQL: {sql}")

                results = result.get('results', [])
                if results is not None:
                    print(f"   结果数量: {len(results)}")
                else:
                    print(f"   结果: 无数据返回")

                context = result.get('context', {})
                if context:
                    print(f"   元数据增强: {context.get('metadata_enhanced', False)}")
                else:
                    print(f"   上下文: 无")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
    
    print("\n🏁 Text2SQL API功能测试完成")

if __name__ == "__main__":
    test_text2sql_api()
