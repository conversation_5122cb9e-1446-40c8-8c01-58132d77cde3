#!/usr/bin/env python3
"""
代码备份与分析脚本
在更新表名引用前，先备份代码并分析需要修改的文件
"""

import os
import shutil
import json
import re
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set

class CodeBackupAnalyzer:
    """代码备份与分析工具"""
    
    def __init__(self):
        self.backup_dir = Path("code_backups")
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.analysis_results = {
            'timestamp': self.timestamp,
            'backup_info': {},
            'files_to_update': [],
            'table_references': {},
            'risk_assessment': {},
            'update_plan': []
        }
        
        # 需要重命名的表映射
        self.table_mappings = {
            'table_descriptions': {
                'resource_db': 'meta_table_descriptions',
                'business_db': 'biz_table_descriptions'
            },
            'column_descriptions': {
                'resource_db': 'meta_column_descriptions', 
                'business_db': 'biz_column_descriptions'
            },
            'business_rules': {
                'resource_db': 'meta_business_rules',
                'business_db': 'biz_business_rules'
            }
        }
        
        # 需要扫描的文件类型
        self.file_patterns = [
            '*.py',
            '*.sql',
            '*.md',
            '*.json',
            '*.yaml',
            '*.yml'
        ]
        
        # 需要排除的目录
        self.exclude_dirs = {
            '__pycache__',
            '.git',
            'node_modules',
            '.venv',
            'venv',
            'database_backups',
            'code_backups',
            '.pytest_cache',
            'frontend',  # 排除前端目录避免路径过长问题
            'dist',
            'build'
        }
    
    def run_analysis(self):
        """运行完整的代码备份与分析"""
        print("🔍 开始代码备份与分析...")
        print("=" * 60)
        
        try:
            # 1. 创建代码备份
            self._create_code_backup()
            
            # 2. 扫描代码文件
            self._scan_code_files()
            
            # 3. 分析表名引用
            self._analyze_table_references()
            
            # 4. 评估风险
            self._assess_risks()
            
            # 5. 制定更新计划
            self._create_update_plan()
            
            # 6. 生成分析报告
            self._generate_analysis_report()
            
            print(f"\n✅ 代码备份与分析完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {e}")
            return False
    
    def _create_code_backup(self):
        """创建代码备份"""
        print(f"\n📁 创建代码备份...")
        
        backup_path = self.backup_dir / self.timestamp
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份主要代码目录
        code_dirs = ['chatdb', '*.py', '*.md']
        
        for pattern in code_dirs:
            if pattern.startswith('*'):
                # 备份根目录下的文件
                for file_path in Path('.').glob(pattern):
                    if file_path.is_file():
                        dest_path = backup_path / file_path.name
                        shutil.copy2(file_path, dest_path)
                        print(f"  ✅ 备份文件: {file_path}")
            else:
                # 备份目录
                src_dir = Path(pattern)
                if src_dir.exists() and src_dir.is_dir():
                    dest_dir = backup_path / pattern
                    shutil.copytree(src_dir, dest_dir, ignore=shutil.ignore_patterns(
                        '__pycache__', '*.pyc', '.git', 'node_modules'
                    ))
                    print(f"  ✅ 备份目录: {src_dir}")
        
        self.analysis_results['backup_info'] = {
            'backup_path': str(backup_path),
            'backup_time': datetime.now().isoformat(),
            'backup_size': self._get_directory_size(backup_path)
        }
        
        print(f"  📊 备份完成，大小: {self.analysis_results['backup_info']['backup_size']:,} bytes")
    
    def _get_directory_size(self, path: Path) -> int:
        """计算目录大小"""
        total_size = 0
        for file_path in path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size
    
    def _scan_code_files(self):
        """扫描代码文件"""
        print(f"\n🔍 扫描代码文件...")
        
        files_to_scan = []
        
        # 扫描所有相关文件
        for pattern in self.file_patterns:
            for file_path in Path('.').rglob(pattern):
                if self._should_include_file(file_path):
                    files_to_scan.append(file_path)
        
        print(f"  📊 发现 {len(files_to_scan)} 个文件需要扫描")
        
        # 分析每个文件
        for file_path in files_to_scan:
            file_analysis = self._analyze_file(file_path)
            if file_analysis['has_table_references']:
                self.analysis_results['files_to_update'].append(file_analysis)
                print(f"  🔍 发现表引用: {file_path}")
        
        print(f"  📊 需要更新的文件: {len(self.analysis_results['files_to_update'])} 个")
    
    def _should_include_file(self, file_path: Path) -> bool:
        """判断是否应该包含此文件"""
        # 排除备份目录
        if any(exclude_dir in file_path.parts for exclude_dir in self.exclude_dirs):
            return False

        # 排除临时文件
        if file_path.name.startswith('.') or file_path.name.endswith('.tmp'):
            return False

        # 排除路径过长的文件（Windows限制）
        if len(str(file_path)) > 250:
            return False

        return True
    
    def _analyze_file(self, file_path: Path) -> Dict:
        """分析单个文件"""
        file_analysis = {
            'file_path': str(file_path),
            'file_type': file_path.suffix,
            'has_table_references': False,
            'table_references': [],
            'line_numbers': [],
            'risk_level': 'LOW'
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 搜索表名引用
            for old_table in self.table_mappings.keys():
                pattern = rf'\b{re.escape(old_table)}\b'
                matches = list(re.finditer(pattern, content, re.IGNORECASE))
                
                if matches:
                    file_analysis['has_table_references'] = True
                    
                    for match in matches:
                        # 找到匹配的行号
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = lines[line_num - 1].strip()
                        
                        file_analysis['table_references'].append({
                            'table_name': old_table,
                            'line_number': line_num,
                            'line_content': line_content,
                            'match_position': match.span()
                        })
                        
                        file_analysis['line_numbers'].append(line_num)
            
            # 评估风险级别
            if file_analysis['has_table_references']:
                file_analysis['risk_level'] = self._assess_file_risk(file_path, file_analysis)
        
        except Exception as e:
            print(f"  ⚠️ 分析文件失败 {file_path}: {e}")
        
        return file_analysis
    
    def _assess_file_risk(self, file_path: Path, file_analysis: Dict) -> str:
        """评估文件修改风险"""
        # 核心模型文件 - 高风险
        if 'models' in file_path.parts and file_path.suffix == '.py':
            return 'HIGH'
        
        # 数据库服务文件 - 高风险
        if 'services' in file_path.parts and any(keyword in file_path.name.lower() 
                                                for keyword in ['db', 'database', 'sql']):
            return 'HIGH'
        
        # SQL查询文件 - 中风险
        if len(file_analysis['table_references']) > 3:
            return 'MEDIUM'
        
        # 文档文件 - 低风险
        if file_path.suffix in ['.md', '.txt']:
            return 'LOW'
        
        return 'MEDIUM'
    
    def _analyze_table_references(self):
        """分析表名引用统计"""
        print(f"\n📊 分析表名引用统计...")
        
        for old_table in self.table_mappings.keys():
            self.analysis_results['table_references'][old_table] = {
                'total_references': 0,
                'files_count': 0,
                'high_risk_files': 0,
                'medium_risk_files': 0,
                'low_risk_files': 0
            }
        
        for file_info in self.analysis_results['files_to_update']:
            for ref in file_info['table_references']:
                table_name = ref['table_name']
                self.analysis_results['table_references'][table_name]['total_references'] += 1
            
            # 统计文件数量
            referenced_tables = set(ref['table_name'] for ref in file_info['table_references'])
            for table_name in referenced_tables:
                self.analysis_results['table_references'][table_name]['files_count'] += 1
                
                # 按风险级别统计
                risk_level = file_info['risk_level']
                if risk_level == 'HIGH':
                    self.analysis_results['table_references'][table_name]['high_risk_files'] += 1
                elif risk_level == 'MEDIUM':
                    self.analysis_results['table_references'][table_name]['medium_risk_files'] += 1
                else:
                    self.analysis_results['table_references'][table_name]['low_risk_files'] += 1
        
        # 打印统计结果
        for table_name, stats in self.analysis_results['table_references'].items():
            print(f"  📋 {table_name}:")
            print(f"    - 总引用数: {stats['total_references']}")
            print(f"    - 涉及文件: {stats['files_count']}")
            print(f"    - 高风险文件: {stats['high_risk_files']}")
            print(f"    - 中风险文件: {stats['medium_risk_files']}")
            print(f"    - 低风险文件: {stats['low_risk_files']}")
    
    def _assess_risks(self):
        """评估整体风险"""
        print(f"\n⚠️ 评估整体风险...")
        
        total_files = len(self.analysis_results['files_to_update'])
        high_risk_files = sum(1 for f in self.analysis_results['files_to_update'] if f['risk_level'] == 'HIGH')
        medium_risk_files = sum(1 for f in self.analysis_results['files_to_update'] if f['risk_level'] == 'MEDIUM')
        
        self.analysis_results['risk_assessment'] = {
            'total_files_to_update': total_files,
            'high_risk_files': high_risk_files,
            'medium_risk_files': medium_risk_files,
            'low_risk_files': total_files - high_risk_files - medium_risk_files,
            'overall_risk': 'LOW'
        }
        
        # 确定整体风险级别
        if high_risk_files > 5:
            self.analysis_results['risk_assessment']['overall_risk'] = 'HIGH'
        elif high_risk_files > 0 or medium_risk_files > 10:
            self.analysis_results['risk_assessment']['overall_risk'] = 'MEDIUM'
        
        risk_info = self.analysis_results['risk_assessment']
        print(f"  📊 总体风险评估: {risk_info['overall_risk']}")
        print(f"  📊 需要更新的文件总数: {risk_info['total_files_to_update']}")
        print(f"  🔴 高风险文件: {risk_info['high_risk_files']}")
        print(f"  🟡 中风险文件: {risk_info['medium_risk_files']}")
        print(f"  🟢 低风险文件: {risk_info['low_risk_files']}")
    
    def _create_update_plan(self):
        """制定更新计划"""
        print(f"\n📋 制定更新计划...")
        
        # 按风险级别和文件类型分组
        update_phases = {
            'phase1_low_risk': [],
            'phase2_medium_risk': [],
            'phase3_high_risk': []
        }
        
        for file_info in self.analysis_results['files_to_update']:
            if file_info['risk_level'] == 'LOW':
                update_phases['phase1_low_risk'].append(file_info)
            elif file_info['risk_level'] == 'MEDIUM':
                update_phases['phase2_medium_risk'].append(file_info)
            else:
                update_phases['phase3_high_risk'].append(file_info)
        
        self.analysis_results['update_plan'] = [
            {
                'phase': 1,
                'name': '低风险文件更新',
                'description': '更新文档、配置文件等低风险文件',
                'files': update_phases['phase1_low_risk'],
                'estimated_time': '30分钟'
            },
            {
                'phase': 2,
                'name': '中风险文件更新',
                'description': '更新一般业务逻辑文件',
                'files': update_phases['phase2_medium_risk'],
                'estimated_time': '1小时'
            },
            {
                'phase': 3,
                'name': '高风险文件更新',
                'description': '更新核心模型和数据库服务文件',
                'files': update_phases['phase3_high_risk'],
                'estimated_time': '2小时'
            }
        ]
        
        for phase in self.analysis_results['update_plan']:
            print(f"  📋 阶段{phase['phase']}: {phase['name']}")
            print(f"    - 文件数量: {len(phase['files'])}")
            print(f"    - 预估时间: {phase['estimated_time']}")
    
    def _generate_analysis_report(self):
        """生成分析报告"""
        report_file = f"code_analysis_report_{self.timestamp}.json"
        
        # 添加汇总信息
        self.analysis_results['summary'] = {
            'analysis_completed_at': datetime.now().isoformat(),
            'backup_created': True,
            'files_analyzed': len(self.analysis_results['files_to_update']),
            'ready_for_update': True
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 分析报告已保存: {report_file}")
        
        # 打印汇总
        print(f"\n📊 分析汇总:")
        print(f"  - 代码备份: ✅ 已完成")
        print(f"  - 需要更新的文件: {self.analysis_results['summary']['files_analyzed']} 个")
        print(f"  - 整体风险级别: {self.analysis_results['risk_assessment']['overall_risk']}")
        print(f"  - 更新阶段: {len(self.analysis_results['update_plan'])} 个")

def main():
    """主函数"""
    print("🔍 代码备份与分析工具")
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = CodeBackupAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print(f"\n🎉 代码备份与分析成功完成！")
        print(f"💡 提示: 现在可以开始分阶段更新代码")
    else:
        print(f"\n❌ 分析过程中出现错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
