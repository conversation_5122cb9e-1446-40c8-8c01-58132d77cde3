#!/usr/bin/env python3
"""
自动更新表名引用脚本
根据分析结果自动更新代码中的表名引用
"""

import os
import re
import json
import shutil
from datetime import datetime
from pathlib import Path

class TableReferenceUpdater:
    """表名引用更新器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 表名映射 - 根据数据库上下文确定使用哪个新表名
        self.table_mappings = {
            'table_descriptions': {
                'default': 'meta_table_descriptions',  # 默认使用meta前缀
                'patterns': {
                    'resource': 'meta_table_descriptions',
                    'meta': 'meta_table_descriptions',
                    'business': 'biz_table_descriptions',
                    'biz': 'biz_table_descriptions',
                    'fin_data': 'biz_table_descriptions'
                }
            },
            'column_descriptions': {
                'default': 'meta_column_descriptions',
                'patterns': {
                    'resource': 'meta_column_descriptions',
                    'meta': 'meta_column_descriptions', 
                    'business': 'biz_column_descriptions',
                    'biz': 'biz_column_descriptions',
                    'fin_data': 'biz_column_descriptions'
                }
            },
            'business_rules': {
                'default': 'meta_business_rules',
                'patterns': {
                    'resource': 'meta_business_rules',
                    'meta': 'meta_business_rules',
                    'business': 'biz_business_rules', 
                    'biz': 'biz_business_rules',
                    'fin_data': 'biz_business_rules'
                }
            }
        }
        
        self.update_log = {
            'timestamp': self.timestamp,
            'files_updated': [],
            'total_replacements': 0,
            'errors': []
        }
        
        # 核心业务文件 - 需要特别小心
        self.critical_files = [
            'chatdb/backend/app/models/metadata_models.py',
            'chatdb/backend/app/services/text2sql_utils.py',
            'chatdb/backend/app/services/text2sql_service.py'
        ]
    
    def update_references(self):
        """更新表名引用"""
        print("🔧 开始更新表名引用...")
        print("=" * 60)
        
        try:
            # 1. 创建代码备份
            self._create_backup()
            
            # 2. 加载分析结果
            analysis_file = self._find_latest_analysis_file()
            if not analysis_file:
                print("❌ 未找到分析结果文件")
                return False
            
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            # 3. 按优先级更新文件
            self._update_files_by_priority(analysis_data)
            
            # 4. 生成更新报告
            self._generate_update_report()
            
            print(f"\n✅ 表名引用更新完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 更新过程中出现错误: {e}")
            self.update_log['errors'].append({
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def _create_backup(self):
        """创建代码备份"""
        print(f"\n📁 创建代码备份...")
        
        backup_dir = Path("code_backups") / f"before_table_update_{self.timestamp}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份chatdb目录
        if Path("chatdb").exists():
            shutil.copytree("chatdb", backup_dir / "chatdb", 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            print(f"  ✅ 备份chatdb目录")
        
        # 备份根目录的Python文件
        for py_file in Path('.').glob('*.py'):
            if py_file.name not in ['06_update_table_references.py']:
                shutil.copy2(py_file, backup_dir / py_file.name)
        
        print(f"  📊 备份完成: {backup_dir}")
        self.update_log['backup_path'] = str(backup_dir)
    
    def _find_latest_analysis_file(self):
        """查找最新的分析结果文件"""
        analysis_files = list(Path('.').glob('simple_code_analysis_*.json'))
        if not analysis_files:
            return None
        
        # 按时间戳排序，返回最新的
        latest_file = max(analysis_files, key=lambda f: f.stat().st_mtime)
        print(f"  📄 使用分析文件: {latest_file}")
        return latest_file
    
    def _update_files_by_priority(self, analysis_data):
        """按优先级更新文件"""
        files_with_refs = analysis_data.get('files_with_references', [])
        
        # 分类文件
        critical_files = []
        normal_files = []
        
        for file_info in files_with_refs:
            file_path = file_info['file_path']
            if any(critical in file_path for critical in self.critical_files):
                critical_files.append(file_info)
            else:
                normal_files.append(file_info)
        
        # 先更新非关键文件
        print(f"\n📋 第一阶段: 更新非关键文件 ({len(normal_files)}个)")
        for file_info in normal_files:
            self._update_single_file(file_info)
        
        # 再更新关键文件
        print(f"\n📋 第二阶段: 更新关键文件 ({len(critical_files)}个)")
        for file_info in critical_files:
            self._update_single_file(file_info, is_critical=True)
    
    def _update_single_file(self, file_info, is_critical=False):
        """更新单个文件"""
        file_path = Path(file_info['file_path'])
        
        if not file_path.exists():
            print(f"  ⚠️ 文件不存在: {file_path}")
            return
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            replacements_made = 0
            
            # 处理每个引用
            for ref in file_info['references']:
                old_table = ref['table_name']
                new_table = self._determine_new_table_name(old_table, content, file_path)
                
                # 执行替换
                content, count = self._replace_table_reference(content, old_table, new_table)
                replacements_made += count
            
            # 如果有更改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                status = "🔴" if is_critical else "✅"
                print(f"  {status} 更新文件: {file_path} ({replacements_made}个替换)")
                
                self.update_log['files_updated'].append({
                    'file_path': str(file_path),
                    'replacements_made': replacements_made,
                    'is_critical': is_critical,
                    'timestamp': datetime.now().isoformat()
                })
                
                self.update_log['total_replacements'] += replacements_made
            else:
                print(f"  ℹ️ 无需更新: {file_path}")
        
        except Exception as e:
            error_msg = f"更新文件失败 {file_path}: {e}"
            print(f"  ❌ {error_msg}")
            self.update_log['errors'].append({
                'error': error_msg,
                'file_path': str(file_path),
                'timestamp': datetime.now().isoformat()
            })
    
    def _determine_new_table_name(self, old_table, content, file_path):
        """根据上下文确定新表名"""
        mapping = self.table_mappings.get(old_table, {})
        default_name = mapping.get('default', old_table)
        
        # 检查文件内容中的上下文线索
        content_lower = content.lower()
        
        # 检查是否有明确的数据库指示
        for pattern, new_name in mapping.get('patterns', {}).items():
            if pattern in content_lower or pattern in str(file_path).lower():
                return new_name
        
        # 特殊处理：如果是SQLAlchemy模型文件，使用meta前缀
        if 'models' in str(file_path) and 'metadata' in str(file_path):
            return mapping.get('patterns', {}).get('meta', default_name)
        
        # 特殊处理：如果文件名包含business或biz，使用biz前缀
        if any(keyword in str(file_path).lower() for keyword in ['business', 'biz', 'fin_data']):
            return mapping.get('patterns', {}).get('business', default_name)
        
        return default_name
    
    def _replace_table_reference(self, content, old_table, new_table):
        """替换表名引用"""
        if old_table == new_table:
            return content, 0
        
        replacements = 0
        
        # 定义替换模式
        patterns = [
            # SQLAlchemy __tablename__
            (rf'(__tablename__\s*=\s*["\'])({re.escape(old_table)})(["\'])', 
             rf'\g<1>{new_table}\g<3>'),
            
            # SQL查询中的表名（带引号）
            (rf'(["\'])({re.escape(old_table)})(["\'])', 
             rf'\g<1>{new_table}\g<3>'),
            
            # SQL FROM子句
            (rf'(FROM\s+)({re.escape(old_table)})(\b)', 
             rf'\g<1>{new_table}\g<3>'),
            
            # SQL INSERT INTO
            (rf'(INSERT\s+INTO\s+)({re.escape(old_table)})(\b)', 
             rf'\g<1>{new_table}\g<3>'),
            
            # SQL UPDATE
            (rf'(UPDATE\s+)({re.escape(old_table)})(\b)', 
             rf'\g<1>{new_table}\g<3>'),
            
            # SQL DELETE FROM
            (rf'(DELETE\s+FROM\s+)({re.escape(old_table)})(\b)', 
             rf'\g<1>{new_table}\g<3>'),
        ]
        
        # 执行替换
        for pattern, replacement in patterns:
            new_content, count = re.subn(pattern, replacement, content, flags=re.IGNORECASE)
            if count > 0:
                content = new_content
                replacements += count
        
        return content, replacements
    
    def _generate_update_report(self):
        """生成更新报告"""
        report_file = f"table_reference_update_report_{self.timestamp}.json"
        
        # 添加汇总信息
        self.update_log['summary'] = {
            'total_files_updated': len(self.update_log['files_updated']),
            'total_replacements': self.update_log['total_replacements'],
            'total_errors': len(self.update_log['errors']),
            'critical_files_updated': len([f for f in self.update_log['files_updated'] if f.get('is_critical', False)]),
            'update_completed_at': datetime.now().isoformat()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.update_log, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 更新报告已保存: {report_file}")
        
        # 打印汇总
        summary = self.update_log['summary']
        print(f"\n📊 更新汇总:")
        print(f"  - 更新文件数: {summary['total_files_updated']}")
        print(f"  - 总替换数: {summary['total_replacements']}")
        print(f"  - 关键文件数: {summary['critical_files_updated']}")
        print(f"  - 错误数: {summary['total_errors']}")
        
        if summary['total_errors'] == 0:
            print(f"  ✅ 所有文件更新成功！")
        else:
            print(f"  ⚠️ 有 {summary['total_errors']} 个错误，请检查日志")

def main():
    """主函数"""
    print("🔧 表名引用更新工具")
    print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    updater = TableReferenceUpdater()
    success = updater.update_references()
    
    if success:
        print(f"\n🎉 表名引用更新成功完成！")
        print(f"💡 提示: 请运行测试验证功能是否正常")
    else:
        print(f"\n❌ 更新过程中出现错误，请检查日志")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
