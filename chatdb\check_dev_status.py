#!/usr/bin/env python3
"""
检查开发环境Docker服务状态
"""
import subprocess
import json
import time

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def check_docker_status():
    """检查Docker服务状态"""
    print("🔍 检查Docker服务状态...")
    
    # 检查Docker是否运行
    success, output, error = run_command("docker info --format json")
    if not success:
        print("❌ Docker服务未运行")
        return False
    
    try:
        docker_info = json.loads(output)
        print(f"✅ Docker服务正常运行")
        print(f"   - 容器总数: {docker_info.get('Containers', 0)}")
        print(f"   - 运行中: {docker_info.get('ContainersRunning', 0)}")
        print(f"   - 镜像数量: {docker_info.get('Images', 0)}")
        return True
    except:
        print("⚠️ Docker信息解析失败")
        return False

def check_dev_containers():
    """检查开发环境容器状态"""
    print("\n🔍 检查开发环境容器状态...")
    
    containers = ["chatdb-mysql-dev", "chatdb-neo4j-dev", "chatdb-redis-dev"]
    
    for container in containers:
        success, output, error = run_command(f"docker ps -a --filter name={container} --format json")
        
        if not output:
            print(f"⚪ {container}: 未创建")
            continue
            
        try:
            container_info = json.loads(output)
            status = container_info.get('State', 'unknown')
            
            if status == 'running':
                print(f"✅ {container}: 运行中")
                
                # 检查端口
                ports = container_info.get('Ports', '')
                if ports:
                    print(f"   - 端口: {ports}")
                    
            elif status == 'exited':
                print(f"❌ {container}: 已停止")
            else:
                print(f"⚠️ {container}: {status}")
                
        except json.JSONDecodeError:
            # 处理非JSON格式输出
            if "Up" in output:
                print(f"✅ {container}: 运行中")
            elif "Exited" in output:
                print(f"❌ {container}: 已停止")
            else:
                print(f"⚠️ {container}: 状态未知")

def check_service_connectivity():
    """检查服务连接性"""
    print("\n🔍 检查服务连接性...")
    
    # 检查MySQL
    success, output, error = run_command(
        'docker exec chatdb-mysql-dev mysql -u root -ppassword -e "SELECT 1" 2>/dev/null'
    )
    if success:
        print("✅ MySQL: 连接正常")
    else:
        print("❌ MySQL: 连接失败")
    
    # 检查Redis
    success, output, error = run_command('docker exec chatdb-redis-dev redis-cli ping')
    if success and "PONG" in output:
        print("✅ Redis: 连接正常")
    else:
        print("❌ Redis: 连接失败")
    
    # 检查Neo4j
    success, output, error = run_command(
        'docker exec chatdb-neo4j-dev cypher-shell -u neo4j -p Di@nhua11 "RETURN 1" 2>/dev/null'
    )
    if success:
        print("✅ Neo4j: 连接正常")
    else:
        print("❌ Neo4j: 连接失败（可能还在启动中）")

def show_connection_info():
    """显示连接信息"""
    print("\n📋 服务连接信息:")
    print("MySQL:")
    print("  - 主机: localhost")
    print("  - 端口: 3306")
    print("  - 用户: root")
    print("  - 密码: password")
    print("  - 数据库: chatdb")
    
    print("\nNeo4j:")
    print("  - HTTP: http://localhost:7474")
    print("  - Bolt: neo4j://localhost:7687")
    print("  - 用户: neo4j")
    print("  - 密码: Di@nhua11")
    
    print("\nRedis:")
    print("  - 主机: localhost")
    print("  - 端口: 6379")
    print("  - 数据库: 0")

def main():
    """主函数"""
    print("🚀 开发环境状态检查工具")
    print("=" * 50)
    
    # 检查Docker状态
    if not check_docker_status():
        return
    
    # 检查容器状态
    check_dev_containers()
    
    # 等待服务启动
    print("\n⏳ 等待服务完全启动...")
    time.sleep(3)
    
    # 检查服务连接性
    check_service_connectivity()
    
    # 显示连接信息
    show_connection_info()
    
    print("\n" + "=" * 50)
    print("💡 提示:")
    print("- 如果服务未运行，请执行: docker-compose -f docker-compose.dev.yml up -d")
    print("- 如果连接失败，请等待几分钟让服务完全启动")
    print("- 查看日志: docker-compose -f docker-compose.dev.yml logs [service_name]")

if __name__ == "__main__":
    main()
