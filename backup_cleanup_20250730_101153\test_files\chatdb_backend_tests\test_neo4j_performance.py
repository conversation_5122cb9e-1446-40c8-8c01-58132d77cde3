"""
Neo4j连接池性能测试脚本
用于验证连接池优化的效果
"""
import asyncio
import time
import logging
import statistics
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_old_connection_method(connection_id: int, num_queries: int = 10) -> Dict[str, Any]:
    """测试旧的连接方法（每次创建新连接）"""
    from neo4j import GraphDatabase
    from app.core.config import settings
    
    times = []
    errors = 0
    
    for i in range(num_queries):
        start_time = time.time()
        try:
            # 每次创建新连接（旧方法）
            driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )
            
            with driver.session() as session:
                result = session.run(
                    "MATCH (t:Table {connection_id: $connection_id}) RETURN count(t) as count",
                    connection_id=connection_id
                )
                data = result.data()
            
            driver.close()
            
            end_time = time.time()
            times.append(end_time - start_time)
            
        except Exception as e:
            errors += 1
            logger.error(f"旧方法查询 {i+1} 失败: {str(e)}")
    
    return {
        "method": "old_connection",
        "total_queries": num_queries,
        "successful_queries": len(times),
        "errors": errors,
        "total_time": sum(times),
        "average_time": statistics.mean(times) if times else 0,
        "min_time": min(times) if times else 0,
        "max_time": max(times) if times else 0,
        "median_time": statistics.median(times) if times else 0
    }


async def test_new_connection_pool_method(connection_id: int, num_queries: int = 10) -> Dict[str, Any]:
    """测试新的连接池方法"""
    from app.services.neo4j_connection_pool import get_neo4j_pool
    
    times = []
    errors = 0
    
    # 初始化连接池
    neo4j_pool = await get_neo4j_pool()
    
    for i in range(num_queries):
        start_time = time.time()
        try:
            # 使用连接池（新方法）
            result = await neo4j_pool.execute_read_query(
                "MATCH (t:Table {connection_id: $connection_id}) RETURN count(t) as count",
                {'connection_id': connection_id}
            )
            
            end_time = time.time()
            times.append(end_time - start_time)
            
        except Exception as e:
            errors += 1
            logger.error(f"新方法查询 {i+1} 失败: {str(e)}")
    
    return {
        "method": "connection_pool",
        "total_queries": num_queries,
        "successful_queries": len(times),
        "errors": errors,
        "total_time": sum(times),
        "average_time": statistics.mean(times) if times else 0,
        "min_time": min(times) if times else 0,
        "max_time": max(times) if times else 0,
        "median_time": statistics.median(times) if times else 0
    }


async def test_concurrent_queries(connection_id: int, num_concurrent: int = 5, queries_per_thread: int = 10) -> Dict[str, Any]:
    """测试并发查询性能"""
    from app.services.neo4j_connection_pool import get_neo4j_pool
    
    neo4j_pool = await get_neo4j_pool()
    
    async def single_thread_queries():
        times = []
        errors = 0
        
        for i in range(queries_per_thread):
            start_time = time.time()
            try:
                result = await neo4j_pool.execute_read_query(
                    "MATCH (t:Table {connection_id: $connection_id}) RETURN count(t) as count",
                    {'connection_id': connection_id}
                )
                end_time = time.time()
                times.append(end_time - start_time)
            except Exception as e:
                errors += 1
                logger.error(f"并发查询失败: {str(e)}")
        
        return times, errors
    
    # 并发执行
    start_time = time.time()
    tasks = [single_thread_queries() for _ in range(num_concurrent)]
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    # 汇总结果
    all_times = []
    total_errors = 0
    
    for times, errors in results:
        all_times.extend(times)
        total_errors += errors
    
    return {
        "method": "concurrent_pool",
        "concurrent_threads": num_concurrent,
        "queries_per_thread": queries_per_thread,
        "total_queries": num_concurrent * queries_per_thread,
        "successful_queries": len(all_times),
        "errors": total_errors,
        "total_time": end_time - start_time,
        "average_query_time": statistics.mean(all_times) if all_times else 0,
        "min_query_time": min(all_times) if all_times else 0,
        "max_query_time": max(all_times) if all_times else 0,
        "median_query_time": statistics.median(all_times) if all_times else 0,
        "queries_per_second": len(all_times) / (end_time - start_time) if (end_time - start_time) > 0 else 0
    }


async def run_performance_comparison():
    """运行性能对比测试"""
    logger.info("🚀 开始Neo4j连接池性能测试...")
    
    connection_id = 1  # 使用测试连接ID
    num_queries = 20   # 每个测试的查询数量
    
    results = {}
    
    # 测试1: 旧连接方法
    logger.info("📊 测试旧连接方法（每次创建新连接）...")
    try:
        results["old_method"] = await test_old_connection_method(connection_id, num_queries)
        logger.info(f"✅ 旧方法测试完成，平均耗时: {results['old_method']['average_time']:.3f}秒")
    except Exception as e:
        logger.error(f"❌ 旧方法测试失败: {str(e)}")
        results["old_method"] = {"error": str(e)}
    
    # 测试2: 新连接池方法
    logger.info("📊 测试新连接池方法...")
    try:
        results["new_method"] = await test_new_connection_pool_method(connection_id, num_queries)
        logger.info(f"✅ 新方法测试完成，平均耗时: {results['new_method']['average_time']:.3f}秒")
    except Exception as e:
        logger.error(f"❌ 新方法测试失败: {str(e)}")
        results["new_method"] = {"error": str(e)}
    
    # 测试3: 并发查询测试
    logger.info("📊 测试并发查询性能...")
    try:
        results["concurrent"] = await test_concurrent_queries(connection_id, num_concurrent=5, queries_per_thread=10)
        logger.info(f"✅ 并发测试完成，QPS: {results['concurrent']['queries_per_second']:.2f}")
    except Exception as e:
        logger.error(f"❌ 并发测试失败: {str(e)}")
        results["concurrent"] = {"error": str(e)}
    
    # 计算性能提升
    if "old_method" in results and "new_method" in results:
        if "error" not in results["old_method"] and "error" not in results["new_method"]:
            old_avg = results["old_method"]["average_time"]
            new_avg = results["new_method"]["average_time"]
            
            if old_avg > 0:
                improvement = ((old_avg - new_avg) / old_avg) * 100
                results["performance_improvement"] = {
                    "time_reduction_percentage": round(improvement, 2),
                    "speedup_factor": round(old_avg / new_avg, 2) if new_avg > 0 else "infinite"
                }
    
    return results


def print_results(results: Dict[str, Any]):
    """打印测试结果"""
    print("\n" + "="*80)
    print("🎯 Neo4j连接池性能测试结果")
    print("="*80)
    
    for method, data in results.items():
        if method == "performance_improvement":
            continue
            
        print(f"\n📊 {method.upper()}:")
        if "error" in data:
            print(f"   ❌ 错误: {data['error']}")
            continue
        
        print(f"   总查询数: {data.get('total_queries', 'N/A')}")
        print(f"   成功查询: {data.get('successful_queries', 'N/A')}")
        print(f"   错误数量: {data.get('errors', 'N/A')}")
        print(f"   总耗时: {data.get('total_time', 0):.3f}秒")
        print(f"   平均耗时: {data.get('average_time', 0):.3f}秒")
        print(f"   最小耗时: {data.get('min_time', 0):.3f}秒")
        print(f"   最大耗时: {data.get('max_time', 0):.3f}秒")
        print(f"   中位耗时: {data.get('median_time', 0):.3f}秒")
        
        if "queries_per_second" in data:
            print(f"   QPS: {data['queries_per_second']:.2f}")
    
    # 打印性能提升信息
    if "performance_improvement" in results:
        improvement = results["performance_improvement"]
        print(f"\n🚀 性能提升:")
        print(f"   时间减少: {improvement['time_reduction_percentage']}%")
        print(f"   速度提升: {improvement['speedup_factor']}倍")
    
    print("\n" + "="*80)


async def main():
    """主函数"""
    try:
        results = await run_performance_comparison()
        print_results(results)
    except Exception as e:
        logger.error(f"性能测试失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
