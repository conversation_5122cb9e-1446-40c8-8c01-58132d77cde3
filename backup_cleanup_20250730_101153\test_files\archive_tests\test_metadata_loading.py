#!/usr/bin/env python3
import sys
sys.path.append('.')

try:
    from app.services.text2sql_utils import get_financial_metadata
    metadata = get_financial_metadata()
    
    print(f'元数据状态: {metadata.get("has_metadata", False)}')
    print(f'字段数量: {len(metadata.get("column_descriptions", []))}')
    print(f'规则数量: {len(metadata.get("business_rules", []))}')
    
    if metadata.get('has_metadata'):
        print('✅ 元数据加载成功')
        
        # 显示一些关键信息
        if metadata.get('table_description'):
            print(f'表描述: {metadata["table_description"]["description"]}')
        
        # 检查关键字段
        column_names = [col['column_name'] for col in metadata.get('column_descriptions', [])]
        key_fields = ['credit_amount', 'debit_amount', 'balance']
        for field in key_fields:
            if field in column_names:
                print(f'✅ 关键字段 {field} 存在')
            else:
                print(f'❌ 关键字段 {field} 缺失')
    else:
        print('❌ 元数据加载失败')
        
except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
