#!/usr/bin/env python3
"""
表冗余分析脚本
分析fin_data.db中的非核心业务数据表是否存在冗余问题
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path

class TableRedundancyAnalyzer:
    """表冗余分析器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.resource_db = "resource.db"
        self.business_db = "fin_data.db"
        
        # 需要分析的表对应关系
        self.table_pairs = {
            'business_rules': {
                'resource_table': 'meta_business_rules',
                'business_table': 'biz_business_rules'
            },
            'column_descriptions': {
                'resource_table': 'meta_column_descriptions',
                'business_table': 'biz_column_descriptions'
            },
            'table_descriptions': {
                'resource_table': 'meta_table_descriptions',
                'business_table': 'biz_table_descriptions'
            }
        }
        
        # 其他需要分析的表
        self.other_tables = [
            'ai_prompt_templates',
            'field_relationships',
            'query_patterns',
            'data_quality_rules'
        ]
        
        self.analysis_results = {
            'timestamp': self.timestamp,
            'redundancy_analysis': {},
            'usage_analysis': {},
            'recommendations': [],
            'cleanup_plan': {}
        }
    
    def analyze_redundancy(self):
        """分析表冗余情况"""
        print("🔍 开始表冗余分析...")
        print("=" * 60)
        
        try:
            # 1. 分析配对表的冗余情况
            self._analyze_paired_tables()
            
            # 2. 分析其他表的用途
            self._analyze_other_tables()
            
            # 3. 分析代码中的使用情况
            self._analyze_code_usage()
            
            # 4. 生成优化建议
            self._generate_recommendations()
            
            # 5. 制定清理计划
            self._create_cleanup_plan()
            
            # 6. 生成分析报告
            self._generate_analysis_report()
            
            print(f"\n✅ 表冗余分析完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 分析过程中出现错误: {e}")
            return False
    
    def _analyze_paired_tables(self):
        """分析配对表的冗余情况"""
        print(f"\n📊 分析配对表冗余情况...")
        
        for table_type, table_info in self.table_pairs.items():
            print(f"\n🔍 分析 {table_type} 表对:")
            
            resource_table = table_info['resource_table']
            business_table = table_info['business_table']
            
            # 分析数据内容差异
            comparison = self._compare_table_data(
                self.resource_db, resource_table,
                self.business_db, business_table
            )
            
            self.analysis_results['redundancy_analysis'][table_type] = comparison
            
            # 打印分析结果
            if comparison['success']:
                print(f"  📊 {resource_table}: {comparison['resource_count']} 条记录")
                print(f"  📊 {business_table}: {comparison['business_count']} 条记录")
                print(f"  🔄 数据重复度: {comparison['overlap_percentage']:.1f}%")
                print(f"  📈 冗余评估: {comparison['redundancy_level']}")
            else:
                print(f"  ❌ 分析失败: {comparison.get('error', '未知错误')}")
    
    def _compare_table_data(self, db1_path, table1, db2_path, table2):
        """比较两个表的数据内容"""
        try:
            # 连接数据库
            conn1 = sqlite3.connect(db1_path)
            conn2 = sqlite3.connect(db2_path)
            
            cursor1 = conn1.cursor()
            cursor2 = conn2.cursor()
            
            # 检查表是否存在
            cursor1.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table1,))
            if not cursor1.fetchone():
                return {'success': False, 'error': f'表 {table1} 不存在于 {db1_path}'}
            
            cursor2.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table2,))
            if not cursor2.fetchone():
                return {'success': False, 'error': f'表 {table2} 不存在于 {db2_path}'}
            
            # 获取记录数量
            cursor1.execute(f"SELECT COUNT(*) FROM {table1}")
            count1 = cursor1.fetchone()[0]
            
            cursor2.execute(f"SELECT COUNT(*) FROM {table2}")
            count2 = cursor2.fetchone()[0]
            
            # 获取表结构
            cursor1.execute(f"PRAGMA table_info({table1})")
            schema1 = [row[1] for row in cursor1.fetchall()]
            
            cursor2.execute(f"PRAGMA table_info({table2})")
            schema2 = [row[1] for row in cursor2.fetchall()]
            
            # 分析数据重叠（基于主键或唯一标识）
            overlap_count = 0
            overlap_percentage = 0
            
            if count1 > 0 and count2 > 0:
                # 尝试找到共同的标识字段
                common_fields = set(schema1) & set(schema2)
                if 'table_name' in common_fields:
                    # 基于table_name比较
                    cursor1.execute(f"SELECT DISTINCT table_name FROM {table1}")
                    keys1 = set(row[0] for row in cursor1.fetchall())
                    
                    cursor2.execute(f"SELECT DISTINCT table_name FROM {table2}")
                    keys2 = set(row[0] for row in cursor2.fetchall())
                    
                    overlap_keys = keys1 & keys2
                    overlap_count = len(overlap_keys)
                    overlap_percentage = (overlap_count / max(len(keys1), len(keys2))) * 100 if max(len(keys1), len(keys2)) > 0 else 0
                
                elif 'id' in common_fields:
                    # 基于内容哈希比较（简化版）
                    overlap_percentage = min(count1, count2) / max(count1, count2) * 100 if max(count1, count2) > 0 else 0
            
            # 评估冗余级别
            if overlap_percentage > 80:
                redundancy_level = "HIGH"
            elif overlap_percentage > 50:
                redundancy_level = "MEDIUM"
            elif overlap_percentage > 20:
                redundancy_level = "LOW"
            else:
                redundancy_level = "MINIMAL"
            
            conn1.close()
            conn2.close()
            
            return {
                'success': True,
                'resource_count': count1,
                'business_count': count2,
                'resource_schema': schema1,
                'business_schema': schema2,
                'schema_match': schema1 == schema2,
                'overlap_count': overlap_count,
                'overlap_percentage': overlap_percentage,
                'redundancy_level': redundancy_level
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_other_tables(self):
        """分析其他表的用途"""
        print(f"\n📊 分析其他表用途...")
        
        for table_name in self.other_tables:
            print(f"\n🔍 分析表: {table_name}")
            
            table_analysis = self._analyze_single_table(self.business_db, table_name)
            self.analysis_results['usage_analysis'][table_name] = table_analysis
            
            if table_analysis['exists']:
                print(f"  📊 记录数: {table_analysis['record_count']}")
                print(f"  📋 字段数: {table_analysis['column_count']}")
                print(f"  🎯 用途评估: {table_analysis['purpose_assessment']}")
            else:
                print(f"  ⚠️ 表不存在")
    
    def _analyze_single_table(self, db_path, table_name):
        """分析单个表"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                return {'exists': False}
            
            # 获取基本信息
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count = cursor.fetchone()[0]
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            column_count = len(columns)
            
            # 评估表的用途
            purpose_assessment = self._assess_table_purpose(table_name, columns, record_count)
            
            conn.close()
            
            return {
                'exists': True,
                'record_count': record_count,
                'column_count': column_count,
                'columns': [col[1] for col in columns],
                'purpose_assessment': purpose_assessment
            }
            
        except Exception as e:
            return {'exists': False, 'error': str(e)}
    
    def _assess_table_purpose(self, table_name, columns, record_count):
        """评估表的用途"""
        column_names = [col[1] for col in columns]
        
        # 基于表名和字段名评估用途
        if table_name == 'ai_prompt_templates':
            if record_count > 0:
                return "SYSTEM_METADATA - AI提示模板，应统一管理"
            else:
                return "UNUSED - 空表，可以删除"
        
        elif table_name == 'field_relationships':
            if 'table_name' in column_names and 'column_name' in column_names:
                return "BUSINESS_METADATA - 字段关系，与业务数据相关"
            else:
                return "UNCLEAR - 需要进一步分析"
        
        elif table_name == 'query_patterns':
            if record_count > 0:
                return "SYSTEM_METADATA - 查询模式，应统一管理"
            else:
                return "UNUSED - 空表，可以删除"
        
        elif table_name == 'data_quality_rules':
            if 'table_name' in column_names:
                return "BUSINESS_METADATA - 数据质量规则，与业务数据相关"
            else:
                return "UNCLEAR - 需要进一步分析"
        
        return "UNCLEAR - 需要进一步分析"
    
    def _analyze_code_usage(self):
        """分析代码中的使用情况"""
        print(f"\n📊 分析代码使用情况...")
        
        # 这里简化处理，基于之前的分析结果
        # 实际应该扫描代码文件查找表名引用
        
        code_usage = {
            'biz_business_rules': {
                'used_in_code': False,
                'usage_context': 'fin_data.db业务规则查询'
            },
            'biz_column_descriptions': {
                'used_in_code': False,
                'usage_context': 'fin_data.db字段描述查询'
            },
            'biz_table_descriptions': {
                'used_in_code': False,
                'usage_context': 'fin_data.db表描述查询'
            },
            'ai_prompt_templates': {
                'used_in_code': True,
                'usage_context': 'AI提示模板管理'
            }
        }
        
        self.analysis_results['code_usage'] = code_usage
        
        for table_name, usage_info in code_usage.items():
            status = "✅ 使用中" if usage_info['used_in_code'] else "⚠️ 未使用"
            print(f"  {status} {table_name}: {usage_info['usage_context']}")
    
    def _generate_recommendations(self):
        """生成优化建议"""
        print(f"\n💡 生成优化建议...")
        
        recommendations = []
        
        # 分析配对表
        for table_type, analysis in self.analysis_results['redundancy_analysis'].items():
            if analysis.get('success', False):
                redundancy_level = analysis.get('redundancy_level', 'UNKNOWN')
                
                if redundancy_level == 'HIGH':
                    recommendations.append({
                        'type': 'REMOVE_REDUNDANT_TABLE',
                        'priority': 'HIGH',
                        'table': self.table_pairs[table_type]['business_table'],
                        'reason': f'与{self.table_pairs[table_type]["resource_table"]}高度重复({analysis["overlap_percentage"]:.1f}%)',
                        'action': f'删除fin_data.db中的{self.table_pairs[table_type]["business_table"]}表'
                    })
                elif redundancy_level in ['MEDIUM', 'LOW']:
                    recommendations.append({
                        'type': 'REVIEW_TABLE_USAGE',
                        'priority': 'MEDIUM',
                        'table': self.table_pairs[table_type]['business_table'],
                        'reason': f'与{self.table_pairs[table_type]["resource_table"]}存在{redundancy_level.lower()}重复({analysis["overlap_percentage"]:.1f}%)',
                        'action': '需要进一步分析具体用途后决定是否保留'
                    })
        
        # 分析其他表
        for table_name, analysis in self.analysis_results['usage_analysis'].items():
            if analysis.get('exists', False):
                purpose = analysis.get('purpose_assessment', '')
                
                if 'UNUSED' in purpose:
                    recommendations.append({
                        'type': 'REMOVE_UNUSED_TABLE',
                        'priority': 'MEDIUM',
                        'table': table_name,
                        'reason': '表为空或未使用',
                        'action': f'删除fin_data.db中的{table_name}表'
                    })
                elif 'SYSTEM_METADATA' in purpose:
                    recommendations.append({
                        'type': 'RELOCATE_TABLE',
                        'priority': 'LOW',
                        'table': table_name,
                        'reason': '属于系统元数据，应统一管理',
                        'action': f'考虑将{table_name}迁移到resource.db'
                    })
        
        self.analysis_results['recommendations'] = recommendations
        
        # 打印建议摘要
        high_priority = [r for r in recommendations if r['priority'] == 'HIGH']
        medium_priority = [r for r in recommendations if r['priority'] == 'MEDIUM']
        low_priority = [r for r in recommendations if r['priority'] == 'LOW']
        
        print(f"  🔴 高优先级建议: {len(high_priority)} 项")
        print(f"  🟡 中优先级建议: {len(medium_priority)} 项")
        print(f"  🟢 低优先级建议: {len(low_priority)} 项")
    
    def _create_cleanup_plan(self):
        """制定清理计划"""
        print(f"\n📋 制定清理计划...")
        
        # 基于建议制定具体的清理步骤
        cleanup_plan = {
            'phase1_safe_removals': [],
            'phase2_conditional_removals': [],
            'phase3_relocations': [],
            'backup_required': True,
            'testing_required': True
        }
        
        for recommendation in self.analysis_results['recommendations']:
            if recommendation['type'] == 'REMOVE_REDUNDANT_TABLE' and recommendation['priority'] == 'HIGH':
                cleanup_plan['phase1_safe_removals'].append(recommendation)
            elif recommendation['type'] in ['REMOVE_UNUSED_TABLE', 'REVIEW_TABLE_USAGE']:
                cleanup_plan['phase2_conditional_removals'].append(recommendation)
            elif recommendation['type'] == 'RELOCATE_TABLE':
                cleanup_plan['phase3_relocations'].append(recommendation)
        
        self.analysis_results['cleanup_plan'] = cleanup_plan
        
        print(f"  📋 第一阶段(安全删除): {len(cleanup_plan['phase1_safe_removals'])} 个表")
        print(f"  📋 第二阶段(条件删除): {len(cleanup_plan['phase2_conditional_removals'])} 个表")
        print(f"  📋 第三阶段(表迁移): {len(cleanup_plan['phase3_relocations'])} 个表")
    
    def _generate_analysis_report(self):
        """生成分析报告"""
        report_file = f"table_redundancy_analysis_{self.timestamp}.json"
        
        # 添加汇总信息
        self.analysis_results['summary'] = {
            'total_recommendations': len(self.analysis_results['recommendations']),
            'high_priority_actions': len([r for r in self.analysis_results['recommendations'] if r['priority'] == 'HIGH']),
            'potential_table_removals': len([r for r in self.analysis_results['recommendations'] if 'REMOVE' in r['type']]),
            'analysis_completed_at': datetime.now().isoformat()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 分析报告已保存: {report_file}")
        
        # 打印汇总
        summary = self.analysis_results['summary']
        print(f"\n📊 分析汇总:")
        print(f"  - 优化建议总数: {summary['total_recommendations']}")
        print(f"  - 高优先级操作: {summary['high_priority_actions']}")
        print(f"  - 可删除表数: {summary['potential_table_removals']}")

def main():
    """主函数"""
    print("🔍 表冗余分析工具")
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = TableRedundancyAnalyzer()
    success = analyzer.analyze_redundancy()
    
    if success:
        print(f"\n🎉 表冗余分析完成！")
        print(f"💡 提示: 请查看分析报告制定具体的优化方案")
    else:
        print(f"\n❌ 分析过程中出现错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
