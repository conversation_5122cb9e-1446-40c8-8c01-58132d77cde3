#!/usr/bin/env python3
"""
统一表结构脚本
解决重复表的结构差异问题
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path

class TableStructureUnifier:
    """表结构统一工具"""
    
    def __init__(self):
        self.resource_db = "resource.db"
        self.business_db = "fin_data.db"
        self.changes_log = {
            'timestamp': datetime.now().isoformat(),
            'changes': [],
            'errors': []
        }
    
    def unify_structures(self):
        """统一表结构"""
        print("🔧 开始统一表结构...")
        print("=" * 60)
        
        try:
            # 1. 统一 column_descriptions 表结构
            self._unify_column_descriptions()
            
            # 2. 同步 business_rules 表数据
            self._sync_business_rules()
            
            # 3. 验证结构统一结果
            self._verify_unification()
            
            # 4. 生成变更报告
            self._generate_change_report()
            
            print(f"\n✅ 表结构统一完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 统一过程中出现错误: {e}")
            self.changes_log['errors'].append({
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def _unify_column_descriptions(self):
        """统一 column_descriptions 表结构"""
        print(f"\n📋 统一 column_descriptions 表结构...")
        
        # 连接resource.db
        resource_conn = sqlite3.connect(self.resource_db)
        resource_cursor = resource_conn.cursor()
        
        # 检查当前结构
        resource_cursor.execute("PRAGMA table_info(column_descriptions)")
        current_columns = [row[1] for row in resource_cursor.fetchall()]
        
        print(f"  📊 当前字段: {len(current_columns)} 个")
        
        # 需要添加的字段
        new_columns = [
            ('field_category', 'TEXT', "''"),
            ('usage_scenarios', 'TEXT', "''"),
            ('common_values', 'TEXT', "''"),
            ('related_fields', 'TEXT', "''"),
            ('calculation_rules', 'TEXT', "''"),
            ('ai_prompt_hints', 'TEXT', "''")
        ]
        
        # 添加缺失的字段
        for column_name, column_type, default_value in new_columns:
            if column_name not in current_columns:
                try:
                    alter_sql = f"ALTER TABLE column_descriptions ADD COLUMN {column_name} {column_type} DEFAULT {default_value}"
                    resource_cursor.execute(alter_sql)
                    
                    print(f"    ✅ 添加字段: {column_name}")
                    
                    self.changes_log['changes'].append({
                        'type': 'ADD_COLUMN',
                        'database': self.resource_db,
                        'table': 'column_descriptions',
                        'column': column_name,
                        'sql': alter_sql,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    print(f"    ❌ 添加字段失败 {column_name}: {e}")
                    self.changes_log['errors'].append({
                        'error': f"添加字段失败 {column_name}: {e}",
                        'timestamp': datetime.now().isoformat()
                    })
            else:
                print(f"    ℹ️ 字段已存在: {column_name}")
        
        # 提交更改
        resource_conn.commit()
        resource_conn.close()
        
        print(f"  ✅ column_descriptions 表结构统一完成")
    
    def _sync_business_rules(self):
        """同步 business_rules 表数据"""
        print(f"\n📋 同步 business_rules 表数据...")
        
        # 连接两个数据库
        resource_conn = sqlite3.connect(self.resource_db)
        business_conn = sqlite3.connect(self.business_db)
        
        resource_cursor = resource_conn.cursor()
        business_cursor = business_conn.cursor()
        
        try:
            # 获取business_db中的数据
            business_cursor.execute("SELECT * FROM business_rules")
            business_rules = business_cursor.fetchall()
            
            # 获取字段名
            business_cursor.execute("PRAGMA table_info(business_rules)")
            columns = [row[1] for row in business_cursor.fetchall()]
            
            print(f"  📊 业务数据库中有 {len(business_rules)} 条规则")
            
            # 获取resource_db中已有的数据
            resource_cursor.execute("SELECT table_name, rule_category, rule_description FROM business_rules")
            existing_rules = set(resource_cursor.fetchall())
            
            print(f"  📊 元数据库中有 {len(existing_rules)} 条规则")
            
            # 找出需要同步的数据
            new_rules = []
            for rule in business_rules:
                # 检查是否已存在（基于table_name, rule_category, rule_description）
                key = (rule[1], rule[2], rule[3])  # table_name, rule_category, rule_description
                if key not in existing_rules:
                    new_rules.append(rule)
            
            print(f"  📊 需要同步 {len(new_rules)} 条新规则")
            
            # 插入新规则
            if new_rules:
                placeholders = ','.join(['?' for _ in columns])
                insert_sql = f"INSERT INTO business_rules ({','.join(columns)}) VALUES ({placeholders})"
                
                for rule in new_rules:
                    try:
                        resource_cursor.execute(insert_sql, rule)
                        print(f"    ✅ 同步规则: {rule[1]} - {rule[2]}")
                        
                        self.changes_log['changes'].append({
                            'type': 'SYNC_DATA',
                            'database': self.resource_db,
                            'table': 'business_rules',
                            'action': 'INSERT',
                            'data': dict(zip(columns, rule)),
                            'timestamp': datetime.now().isoformat()
                        })
                        
                    except Exception as e:
                        print(f"    ❌ 同步失败: {e}")
                        self.changes_log['errors'].append({
                            'error': f"同步规则失败: {e}",
                            'data': dict(zip(columns, rule)),
                            'timestamp': datetime.now().isoformat()
                        })
            
            # 提交更改
            resource_conn.commit()
            
        except Exception as e:
            print(f"  ❌ 同步过程出错: {e}")
            self.changes_log['errors'].append({
                'error': f"同步business_rules失败: {e}",
                'timestamp': datetime.now().isoformat()
            })
        
        finally:
            resource_conn.close()
            business_conn.close()
        
        print(f"  ✅ business_rules 表数据同步完成")
    
    def _verify_unification(self):
        """验证结构统一结果"""
        print(f"\n🔍 验证结构统一结果...")
        
        verification_results = {}
        
        # 验证 column_descriptions 表结构
        verification_results['column_descriptions'] = self._verify_column_descriptions_structure()
        
        # 验证 business_rules 表数据
        verification_results['business_rules'] = self._verify_business_rules_data()
        
        # 打印验证结果
        for table, result in verification_results.items():
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {table}: {result['message']}")
        
        self.changes_log['verification'] = verification_results
        
        return all(result['success'] for result in verification_results.values())
    
    def _verify_column_descriptions_structure(self):
        """验证 column_descriptions 表结构"""
        try:
            # 连接两个数据库
            resource_conn = sqlite3.connect(self.resource_db)
            business_conn = sqlite3.connect(self.business_db)
            
            # 获取两个数据库的表结构
            resource_cursor = resource_conn.cursor()
            business_cursor = business_conn.cursor()
            
            resource_cursor.execute("PRAGMA table_info(column_descriptions)")
            resource_columns = set(row[1] for row in resource_cursor.fetchall())
            
            business_cursor.execute("PRAGMA table_info(column_descriptions)")
            business_columns = set(row[1] for row in business_cursor.fetchall())
            
            resource_conn.close()
            business_conn.close()
            
            # 检查结构是否一致
            if resource_columns == business_columns:
                return {
                    'success': True,
                    'message': f'结构一致，共{len(resource_columns)}个字段'
                }
            else:
                missing_in_resource = business_columns - resource_columns
                missing_in_business = resource_columns - business_columns
                
                return {
                    'success': False,
                    'message': f'结构不一致，resource缺少{len(missing_in_resource)}个字段，business缺少{len(missing_in_business)}个字段',
                    'missing_in_resource': list(missing_in_resource),
                    'missing_in_business': list(missing_in_business)
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'验证失败: {e}'
            }
    
    def _verify_business_rules_data(self):
        """验证 business_rules 表数据"""
        try:
            # 连接两个数据库
            resource_conn = sqlite3.connect(self.resource_db)
            business_conn = sqlite3.connect(self.business_db)
            
            resource_cursor = resource_conn.cursor()
            business_cursor = business_conn.cursor()
            
            # 获取数据行数
            resource_cursor.execute("SELECT COUNT(*) FROM business_rules")
            resource_count = resource_cursor.fetchone()[0]
            
            business_cursor.execute("SELECT COUNT(*) FROM business_rules")
            business_count = business_cursor.fetchone()[0]
            
            resource_conn.close()
            business_conn.close()
            
            return {
                'success': True,
                'message': f'resource: {resource_count}条, business: {business_count}条',
                'resource_count': resource_count,
                'business_count': business_count
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'验证失败: {e}'
            }
    
    def _generate_change_report(self):
        """生成变更报告"""
        report_file = f"table_structure_changes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加汇总信息
        self.changes_log['summary'] = {
            'total_changes': len(self.changes_log['changes']),
            'total_errors': len(self.changes_log['errors']),
            'success': len(self.changes_log['errors']) == 0,
            'completed_at': datetime.now().isoformat()
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.changes_log, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 变更报告已保存: {report_file}")
        
        # 打印汇总
        print(f"\n📊 变更汇总:")
        print(f"  - 执行变更: {self.changes_log['summary']['total_changes']} 项")
        print(f"  - 错误数量: {self.changes_log['summary']['total_errors']} 项")
        print(f"  - 整体状态: {'✅ 成功' if self.changes_log['summary']['success'] else '❌ 有错误'}")

def main():
    """主函数"""
    print("🔧 表结构统一工具")
    print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    unifier = TableStructureUnifier()
    success = unifier.unify_structures()
    
    if success:
        print(f"\n🎉 表结构统一成功完成！")
        print(f"💡 提示: 现在可以进行下一步的表重命名操作")
    else:
        print(f"\n❌ 表结构统一过程中出现错误，请检查日志")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
