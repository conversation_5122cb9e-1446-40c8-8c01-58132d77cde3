#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3

def check_resource_db_status():
    """检查resource.db的确切状态"""
    
    # 检查resource.db的确切状态
    resource_path = r'C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db'
    print('🔍 检查resource.db的确切状态')
    print('=' * 60)
    print(f'完整路径: {resource_path}')
    print(f'文件存在: {os.path.exists(resource_path)}')

    if os.path.exists(resource_path):
        print(f'文件大小: {os.path.getsize(resource_path)} 字节')
        
        # 尝试连接并查看表结构
        try:
            conn = sqlite3.connect(resource_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f'表数量: {len(tables)}')
            print(f'表列表: {tables}')
            
            # 检查关键表
            key_tables = ['financial_data', 'table_descriptions', 'column_descriptions', 'business_rules']
            for table in key_tables:
                if table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    count = cursor.fetchone()[0]
                    print(f'✅ {table}: {count:,} 条记录')
                else:
                    print(f'❌ {table}: 不存在')
            
            # 检查系统表
            system_tables = ['dbconnection', 'schematable', 'schemacolumn']
            print(f'\n⚙️ 系统表检查:')
            for table in system_tables:
                if table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    count = cursor.fetchone()[0]
                    print(f'✅ {table}: {count} 条记录')
                else:
                    print(f'❌ {table}: 不存在')
            
            conn.close()
        except Exception as e:
            print(f'连接错误: {e}')
    else:
        print('❌ 文件确实不存在')
        
        # 检查目录中的其他.db文件
        directory = os.path.dirname(resource_path)
        print(f'\n📁 检查目录中的.db文件:')
        try:
            for file in os.listdir(directory):
                if file.endswith('.db'):
                    full_path = os.path.join(directory, file)
                    size = os.path.getsize(full_path)
                    print(f'   📄 {file}: {size:,} 字节')
        except Exception as e:
            print(f'目录读取错误: {e}')

def check_current_config():
    """检查当前配置状态"""
    print(f'\n🔧 检查当前配置状态')
    print('=' * 60)
    
    # 检查.env文件
    env_file = 'chatdb/backend/.env'
    if os.path.exists(env_file):
        print(f'✅ 配置文件存在: {env_file}')
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if 'SQLITE_DB_PATH' in line:
                    print(f'   第{line_num}行: {line.strip()}')
    else:
        print(f'❌ 配置文件不存在: {env_file}')
    
    # 检查fin_data.db
    fin_data_path = 'fin_data.db'
    if os.path.exists(fin_data_path):
        size = os.path.getsize(fin_data_path)
        print(f'✅ fin_data.db存在: {size:,} 字节')
        
        # 快速检查表数量
        try:
            conn = sqlite3.connect(fin_data_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f'   表数量: {table_count}')
            conn.close()
        except Exception as e:
            print(f'   检查错误: {e}')
    else:
        print(f'❌ fin_data.db不存在')

def analyze_situation():
    """分析当前情况并给出建议"""
    print(f'\n📊 情况分析与建议')
    print('=' * 60)
    
    resource_exists = os.path.exists(r'C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db')
    fin_data_exists = os.path.exists('fin_data.db')
    
    print(f'resource.db存在: {resource_exists}')
    print(f'fin_data.db存在: {fin_data_exists}')
    
    if resource_exists and fin_data_exists:
        print(f'\n🎯 建议: 需要决定使用哪个数据库')
        print(f'1. 如果resource.db包含系统数据，考虑将元数据迁移过去')
        print(f'2. 如果resource.db为空或过时，继续使用fin_data.db')
    elif resource_exists and not fin_data_exists:
        print(f'\n🎯 建议: 将元数据迁移到resource.db')
    elif not resource_exists and fin_data_exists:
        print(f'\n🎯 建议: 继续使用fin_data.db配置（当前方案正确）')
    else:
        print(f'\n❌ 问题: 两个数据库都不存在')

if __name__ == '__main__':
    check_resource_db_status()
    check_current_config()
    analyze_situation()
