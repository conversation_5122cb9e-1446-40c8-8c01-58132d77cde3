#!/usr/bin/env python3
"""
手动表名更新脚本
针对核心文件进行精确的表名更新
"""

import re
from pathlib import Path
from datetime import datetime

class ManualTableUpdater:
    """手动表名更新器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.updates_made = []
        
        # 核心文件列表 - 只更新最重要的文件
        self.core_files = [
            'chatdb/backend/app/models/metadata_models.py',
            'chatdb/backend/app/services/text2sql_utils.py',
            'chatdb/backend/app/services/text2sql_service.py',
            'chatdb/backend/app/services/enhanced_prompt_service.py'
        ]
    
    def update_core_files(self):
        """更新核心文件"""
        print("🔧 开始手动更新核心文件...")
        print("=" * 50)
        
        for file_path in self.core_files:
            if Path(file_path).exists():
                print(f"\n📝 更新文件: {file_path}")
                self._update_file(file_path)
            else:
                print(f"\n⚠️ 文件不存在: {file_path}")
        
        self._print_summary()
    
    def _update_file(self, file_path):
        """更新单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 根据文件类型和内容确定更新策略
            if 'metadata_models.py' in file_path:
                content = self._update_metadata_models(content)
            elif 'text2sql_utils.py' in file_path:
                content = self._update_text2sql_utils(content)
            elif 'text2sql_service.py' in file_path:
                content = self._update_text2sql_service(content)
            elif 'enhanced_prompt_service.py' in file_path:
                content = self._update_enhanced_prompt_service(content)
            
            # 如果有更改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                changes = self._count_changes(original_content, content)
                print(f"  ✅ 更新完成，{changes}个更改")
                self.updates_made.append({
                    'file': file_path,
                    'changes': changes
                })
            else:
                print(f"  ℹ️ 无需更新")
        
        except Exception as e:
            print(f"  ❌ 更新失败: {e}")
    
    def _update_metadata_models(self, content):
        """更新metadata_models.py文件"""
        # 这个文件中的column_descriptions应该使用meta前缀，因为它是元数据模型
        content = re.sub(
            r'__tablename__\s*=\s*["\']column_descriptions["\']',
            '__tablename__ = "meta_column_descriptions"',
            content
        )
        return content
    
    def _update_text2sql_utils(self, content):
        """更新text2sql_utils.py文件"""
        # 这个文件主要处理元数据查询，使用meta前缀
        replacements = [
            (r'FROM\s+table_descriptions\b', 'FROM meta_table_descriptions'),
            (r'FROM\s+column_descriptions\b', 'FROM meta_column_descriptions'),
            (r'FROM\s+business_rules\b', 'FROM meta_business_rules'),
            (r'["\']table_descriptions["\']', '"meta_table_descriptions"'),
            (r'["\']column_descriptions["\']', '"meta_column_descriptions"'),
            (r'["\']business_rules["\']', '"meta_business_rules"'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        return content
    
    def _update_text2sql_service(self, content):
        """更新text2sql_service.py文件"""
        # 这个文件也主要处理元数据，使用meta前缀
        replacements = [
            (r'["\']column_descriptions["\']', '"meta_column_descriptions"'),
            (r'["\']business_rules["\']', '"meta_business_rules"'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        return content
    
    def _update_enhanced_prompt_service(self, content):
        """更新enhanced_prompt_service.py文件"""
        # 这个文件处理提示增强，使用meta前缀
        replacements = [
            (r'["\']column_descriptions["\']', '"meta_column_descriptions"'),
            (r'["\']business_rules["\']', '"meta_business_rules"'),
            (r'FROM\s+column_descriptions\b', 'FROM meta_column_descriptions'),
            (r'FROM\s+business_rules\b', 'FROM meta_business_rules'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        return content
    
    def _count_changes(self, original, updated):
        """计算更改数量"""
        original_lines = original.split('\n')
        updated_lines = updated.split('\n')
        
        changes = 0
        for i, (orig, upd) in enumerate(zip(original_lines, updated_lines)):
            if orig != upd:
                changes += 1
        
        return changes
    
    def _print_summary(self):
        """打印更新摘要"""
        print(f"\n📊 更新摘要:")
        print(f"  - 处理文件数: {len(self.core_files)}")
        print(f"  - 成功更新数: {len(self.updates_made)}")
        
        if self.updates_made:
            print(f"\n📋 更新详情:")
            for update in self.updates_made:
                print(f"  - {update['file']}: {update['changes']}个更改")
        
        total_changes = sum(update['changes'] for update in self.updates_made)
        print(f"\n✅ 总计: {total_changes}个更改")

def main():
    """主函数"""
    print("🔧 手动表名更新工具")
    print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    updater = ManualTableUpdater()
    updater.update_core_files()
    
    print(f"\n🎉 核心文件更新完成！")
    print(f"💡 提示: 请测试核心功能是否正常工作")
    
    return 0

if __name__ == "__main__":
    exit(main())
