#!/usr/bin/env python3
"""
简化版代码分析脚本
专注于查找需要更新的表名引用
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path

class SimpleCodeAnalyzer:
    """简化版代码分析器"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 需要重命名的表
        self.old_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
        
        # 表名映射
        self.table_mappings = {
            'table_descriptions': {
                'resource_db': 'meta_table_descriptions',
                'business_db': 'biz_table_descriptions'
            },
            'column_descriptions': {
                'resource_db': 'meta_column_descriptions', 
                'business_db': 'biz_column_descriptions'
            },
            'business_rules': {
                'resource_db': 'meta_business_rules',
                'business_db': 'biz_business_rules'
            }
        }
        
        self.results = {
            'timestamp': self.timestamp,
            'files_with_references': [],
            'summary': {}
        }
    
    def analyze(self):
        """执行分析"""
        print("🔍 开始简化版代码分析...")
        print("=" * 50)
        
        # 分析主要目录
        self._analyze_directory('chatdb/backend')
        
        # 分析根目录的Python文件
        self._analyze_root_files()
        
        # 生成报告
        self._generate_report()
        
        return self.results
    
    def _analyze_directory(self, dir_path):
        """分析指定目录"""
        print(f"\n📁 分析目录: {dir_path}")
        
        if not Path(dir_path).exists():
            print(f"  ⚠️ 目录不存在: {dir_path}")
            return
        
        # 查找Python文件
        for py_file in Path(dir_path).rglob('*.py'):
            if self._should_analyze_file(py_file):
                self._analyze_file(py_file)
    
    def _analyze_root_files(self):
        """分析根目录文件"""
        print(f"\n📁 分析根目录文件...")
        
        for file_path in Path('.').glob('*.py'):
            if file_path.name != '04_code_backup_and_analysis.py':  # 排除自己
                self._analyze_file(file_path)
    
    def _should_analyze_file(self, file_path):
        """判断是否应该分析此文件"""
        # 排除特定目录
        exclude_parts = ['__pycache__', '.git', 'node_modules', 'venv', '.venv']
        if any(part in file_path.parts for part in exclude_parts):
            return False
        
        # 只分析Python文件
        if file_path.suffix != '.py':
            return False
        
        return True
    
    def _analyze_file(self, file_path):
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            file_info = {
                'file_path': str(file_path),
                'references': []
            }
            
            # 查找表名引用
            for table_name in self.old_tables:
                # 使用正则表达式查找表名
                patterns = [
                    rf'["\']({re.escape(table_name)})["\']',  # 字符串中的表名
                    rf'__tablename__\s*=\s*["\']({re.escape(table_name)})["\']',  # SQLAlchemy表名
                    rf'FROM\s+({re.escape(table_name)})\b',  # SQL FROM子句
                    rf'INSERT\s+INTO\s+({re.escape(table_name)})\b',  # SQL INSERT
                    rf'UPDATE\s+({re.escape(table_name)})\b',  # SQL UPDATE
                    rf'DELETE\s+FROM\s+({re.escape(table_name)})\b',  # SQL DELETE
                ]
                
                for pattern in patterns:
                    matches = list(re.finditer(pattern, content, re.IGNORECASE))
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = content.split('\n')[line_num - 1].strip()
                        
                        file_info['references'].append({
                            'table_name': table_name,
                            'line_number': line_num,
                            'line_content': line_content,
                            'pattern_type': pattern
                        })
            
            # 如果找到引用，添加到结果中
            if file_info['references']:
                self.results['files_with_references'].append(file_info)
                print(f"  🔍 发现引用: {file_path} ({len(file_info['references'])}个)")
        
        except Exception as e:
            print(f"  ❌ 分析文件失败 {file_path}: {e}")
    
    def _generate_report(self):
        """生成分析报告"""
        print(f"\n📊 生成分析报告...")
        
        # 统计信息
        total_files = len(self.results['files_with_references'])
        total_references = sum(len(f['references']) for f in self.results['files_with_references'])
        
        # 按表名统计
        table_stats = {}
        for table_name in self.old_tables:
            table_stats[table_name] = {
                'files': 0,
                'references': 0
            }
        
        for file_info in self.results['files_with_references']:
            referenced_tables = set()
            for ref in file_info['references']:
                table_name = ref['table_name']
                table_stats[table_name]['references'] += 1
                referenced_tables.add(table_name)
            
            for table_name in referenced_tables:
                table_stats[table_name]['files'] += 1
        
        self.results['summary'] = {
            'total_files_with_references': total_files,
            'total_references': total_references,
            'table_statistics': table_stats,
            'analysis_completed_at': datetime.now().isoformat()
        }
        
        # 保存详细报告
        report_file = f"simple_code_analysis_{self.timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 打印汇总
        print(f"\n📋 分析结果汇总:")
        print(f"  - 包含表引用的文件: {total_files} 个")
        print(f"  - 总引用数量: {total_references} 个")
        print(f"\n📊 各表统计:")
        for table_name, stats in table_stats.items():
            print(f"  - {table_name}: {stats['files']}个文件, {stats['references']}个引用")
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        # 生成更新建议
        self._generate_update_suggestions()
    
    def _generate_update_suggestions(self):
        """生成更新建议"""
        print(f"\n💡 更新建议:")
        
        if not self.results['files_with_references']:
            print(f"  ✅ 未发现需要更新的表名引用")
            return
        
        print(f"  📋 需要更新的文件清单:")
        for file_info in self.results['files_with_references']:
            print(f"    - {file_info['file_path']}")
            for ref in file_info['references']:
                print(f"      第{ref['line_number']}行: {ref['table_name']}")
        
        print(f"\n🔧 建议的更新步骤:")
        print(f"  1. 备份当前代码")
        print(f"  2. 按文件逐个更新表名引用")
        print(f"  3. 每个文件更新后进行测试")
        print(f"  4. 确认所有功能正常后提交更改")
        
        print(f"\n📋 表名映射参考:")
        for old_table, mappings in self.table_mappings.items():
            print(f"  - {old_table}:")
            print(f"    resource.db → {mappings['resource_db']}")
            print(f"    fin_data.db → {mappings['business_db']}")

def main():
    """主函数"""
    print("🔍 简化版代码分析工具")
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = SimpleCodeAnalyzer()
    results = analyzer.analyze()
    
    print(f"\n✅ 分析完成！")
    return 0

if __name__ == "__main__":
    exit(main())
