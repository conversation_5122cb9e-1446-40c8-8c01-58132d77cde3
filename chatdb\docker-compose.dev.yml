version: '3.8'

# 开发环境Docker Compose配置
# 用于快速启动开发所需的基础服务（数据库、缓存等）
# 不包含应用服务，应用在本地运行以便于调试

services:
  # mysql:
  #   image: swr.cn-east-3.myhuaweicloud.com/library/mysql:8.0
  #   container_name: chatdb-mysql-dev
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: password
  #     MYSQL_DATABASE: chatdb
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_dev_data:/var/lib/mysql
  #   command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # neo4j:
  #   image: neo4j:4.4
  #   container_name: chatdb-neo4j-dev
  #   restart: unless-stopped
  #   environment:
  #     NEO4J_AUTH: neo4j/Di@nhua11  # 使用.env文件中的密码
  #   ports:
  #     - "7474:7474"  # HTTP
  #     - "7687:7687"  # Bolt
  #   volumes:
  #     - neo4j_dev_data:/data
  #     - neo4j_dev_logs:/logs

  redis:
    image: redis:6-alpine
    container_name: chatdb-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：Milvus向量数据库（如果需要）
  # milvus:
  #   image: milvusdb/milvus:v2.3.0
  #   container_name: chatdb-milvus-dev
  #   restart: unless-stopped
  #   ports:
  #     - "19530:19530"
  #   volumes:
  #     - milvus_dev_data:/var/lib/milvus
  #   environment:
  #     ETCD_ENDPOINTS: etcd:2379
  #     MINIO_ADDRESS: minio:9000

volumes:
  # mysql_dev_data:
  #   driver: local
  neo4j_dev_data:
    driver: local
  neo4j_dev_logs:
    driver: local
  redis_dev_data:
    driver: local
  # milvus_dev_data:
  #   driver: local

networks:
  default:
    name: chatdb-dev-network
