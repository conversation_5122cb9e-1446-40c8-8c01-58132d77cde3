#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
元数据集成测试脚本
验证Text2SQL服务是否正确集成了元数据系统
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加项目路径
sys.path.append('chatdb/backend')

def test_metadata_loading():
    """测试元数据加载功能"""
    print("🧪 测试1: 元数据加载功能")
    print("-" * 50)
    
    try:
        # 直接测试元数据获取函数
        from app.services.text2sql_utils import get_financial_metadata
        
        metadata = get_financial_metadata("financial_data")
        
        if metadata.get("has_metadata"):
            print("✅ 元数据加载成功")
            print(f"   表描述: {'✅' if metadata.get('table_description') else '❌'}")
            print(f"   字段数量: {len(metadata.get('column_descriptions', []))}")
            print(f"   业务规则数量: {len(metadata.get('business_rules', []))}")
            
            # 检查关键字段
            column_names = [col['column_name'] for col in metadata.get('column_descriptions', [])]
            key_fields = ['credit_amount', 'debit_amount', 'balance']
            for field in key_fields:
                if field in column_names:
                    print(f"   ✅ 关键字段 {field} 存在")
                else:
                    print(f"   ❌ 关键字段 {field} 缺失")
            
            return True
        else:
            print("❌ 元数据加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n🧪 测试2: API集成测试")
    print("-" * 50)
    
    base_url = "http://localhost:8000/v1"
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未运行，请先启动服务")
            print("   启动命令: cd chatdb/backend && python -m uvicorn app.main:app --reload")
            return False
    except requests.exceptions.RequestException:
        print("❌ 无法连接到后端服务")
        print("   请确保服务在 http://localhost:8000 运行")
        return False
    
    print("✅ 后端服务运行正常")
    
    # 测试查询
    test_cases = [
        {
            "name": "收入查询测试",
            "query": "查询2024年9月的收入情况",
            "expected_keywords": ["credit_amount", "60"],
            "description": "应该使用credit_amount字段和60xx科目编号"
        },
        {
            "name": "资产查询测试", 
            "query": "显示2024年9月的资产余额",
            "expected_keywords": ["balance", "CAST", "1"],
            "description": "应该使用balance字段、类型转换和1xxx科目编号"
        },
        {
            "name": "费用查询测试",
            "query": "分析2024年9月的管理费用",
            "expected_keywords": ["debit_amount", "64"],
            "description": "应该使用debit_amount字段和64xx科目编号"
        }
    ]
    
    success_count = 0
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n   测试 {i}: {test['name']}")
        
        try:
            response = requests.post(
                f"{base_url}/query/",
                json={
                    "query": test["query"],
                    "connection_id": 1
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                sql = result.get("sql", "").lower()
                context = result.get("context", {})
                
                # 检查是否应用了元数据增强
                if context.get("metadata_enhanced"):
                    print(f"      ✅ 元数据增强已应用")
                    
                    # 检查业务规则应用数量
                    rules_count = context.get("business_rules_applied", 0)
                    print(f"      ✅ 应用了 {rules_count} 个业务规则")
                else:
                    print(f"      ❌ 元数据增强未应用")
                
                # 检查关键词
                keyword_found = 0
                for keyword in test["expected_keywords"]:
                    if keyword.lower() in sql:
                        print(f"      ✅ 找到关键词: {keyword}")
                        keyword_found += 1
                    else:
                        print(f"      ❌ 缺少关键词: {keyword}")
                
                if keyword_found >= len(test["expected_keywords"]) // 2:  # 至少一半关键词匹配
                    print(f"      ✅ SQL生成符合预期")
                    success_count += 1
                else:
                    print(f"      ❌ SQL生成不符合预期")
                
                print(f"      📝 生成的SQL: {result.get('sql', '')[:100]}...")
                
            else:
                print(f"      ❌ API请求失败: {response.status_code}")
                if response.text:
                    print(f"      错误信息: {response.text[:200]}")
                
        except Exception as e:
            print(f"      ❌ 测试异常: {e}")
    
    print(f"\n📊 API测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)

def test_configuration():
    """测试配置"""
    print("\n🧪 测试3: 配置检查")
    print("-" * 50)
    
    try:
        from app.core.config import settings
        
        # 检查配置项
        configs = [
            ("SQLITE_DB_PATH", settings.SQLITE_DB_PATH),
            ("METADATA_DB_PATH", getattr(settings, 'METADATA_DB_PATH', None)),
            ("BUSINESS_DB_PATH", getattr(settings, 'BUSINESS_DB_PATH', None)),
            ("ENABLE_METADATA_ENHANCEMENT", getattr(settings, 'ENABLE_METADATA_ENHANCEMENT', None))
        ]
        
        for name, value in configs:
            if value is not None:
                print(f"✅ {name}: {value}")
            else:
                print(f"❌ {name}: 未配置")
        
        # 检查数据库文件
        db_files = [
            ("resource.db", "元数据和系统表"),
            ("fin_data.db", "业务数据")
        ]
        
        for db_file, description in db_files:
            if os.path.exists(db_file):
                size = os.path.getsize(db_file)
                print(f"✅ {db_file} ({description}): {size:,} 字节")
            else:
                print(f"❌ {db_file} ({description}): 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 元数据集成测试报告")
    print("=" * 60)
    
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试项目: {len(results)}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n📈 测试结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！元数据集成成功！")
        print("\n📋 下一步:")
        print("1. 部署到生产环境")
        print("2. 监控查询质量")
        print("3. 收集用户反馈")
    else:
        print("⚠️  部分测试失败，需要检查和修复")
        print("\n📋 建议:")
        if not results.get("元数据加载"):
            print("- 检查数据库文件和元数据表")
        if not results.get("API集成"):
            print("- 检查服务启动和代码集成")
        if not results.get("配置检查"):
            print("- 检查环境变量和配置文件")

def main():
    """主测试函数"""
    print("🎯 元数据集成测试")
    print("=" * 60)
    print("此测试将验证Text2SQL服务是否正确集成了元数据系统")
    print()
    
    # 执行测试
    results = {}
    
    # 测试1: 元数据加载
    results["元数据加载"] = test_metadata_loading()
    
    # 测试2: API集成
    results["API集成"] = test_api_integration()
    
    # 测试3: 配置检查
    results["配置检查"] = test_configuration()
    
    # 生成报告
    generate_test_report(results)

if __name__ == "__main__":
    main()
