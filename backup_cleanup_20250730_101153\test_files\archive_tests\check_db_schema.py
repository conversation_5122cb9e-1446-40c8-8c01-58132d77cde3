import sqlite3
import json

def analyze_database_schema():
    """分析数据库架构"""
    try:
        conn = sqlite3.connect('fin_data.db')
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("=== 数据库表列表 ===")
        for table in tables:
            print(f"- {table[0]}")
        
        # 分析financial_data表结构
        if ('financial_data',) in tables:
            print("\n=== financial_data表结构 ===")
            cursor.execute('PRAGMA table_info(financial_data)')
            columns = cursor.fetchall()
            for col in columns:
                print(f"列名: {col[1]}, 类型: {col[2]}, 非空: {col[3]}, 默认值: {col[4]}, 主键: {col[5]}")
            
            # 获取样本数据
            print("\n=== financial_data样本数据 ===")
            cursor.execute('SELECT * FROM financial_data LIMIT 3')
            sample_data = cursor.fetchall()
            for row in sample_data:
                print(row)
                
            # 统计记录数
            cursor.execute('SELECT COUNT(*) FROM financial_data')
            count = cursor.fetchone()[0]
            print(f"\n总记录数: {count}")
        
        # 检查其他重要表
        for table_name in ['main', 'sqlite_sequence']:
            if (table_name,) in tables:
                print(f"\n=== {table_name}表结构 ===")
                cursor.execute(f'PRAGMA table_info({table_name})')
                columns = cursor.fetchall()
                for col in columns:
                    print(f"列名: {col[1]}, 类型: {col[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"分析数据库时出错: {e}")

if __name__ == "__main__":
    analyze_database_schema()
