#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的元数据系统
独立测试版本，不依赖chatdb后端
"""

import sqlite3
from typing import Dict, Any, List

def get_financial_metadata(connection_path: str, table_name: str = "financial_data") -> Dict[str, Any]:
    """获取财务表的元数据信息"""
    try:
        conn = sqlite3.connect(connection_path)
        cursor = conn.cursor()
        
        metadata = {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }
        
        # 检查是否存在元数据表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'")
        if not cursor.fetchone():
            print("⚠️  未找到元数据表，使用基础模式")
            conn.close()
            return metadata
        
        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale 
            FROM table_descriptions 
            WHERE table_name = ?
        """, (table_name,))
        
        table_desc = cursor.fetchone()
        if table_desc:
            metadata["table_description"] = {
                "description": table_desc[0],
                "business_purpose": table_desc[1],
                "data_scale": table_desc[2]
            }
        
        # 查询字段描述
        cursor.execute("""
            SELECT column_name, chinese_name, description, data_type, 
                   business_rules, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = ?
            ORDER BY column_name
        """, (table_name,))
        
        column_descs = cursor.fetchall()
        for col in column_descs:
            metadata["column_descriptions"].append({
                "column_name": col[0],
                "chinese_name": col[1],
                "description": col[2],
                "data_type": col[3],
                "business_rules": col[4],
                "ai_understanding_points": col[5]
            })
        
        # 查询关键业务规则
        cursor.execute("""
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM business_rules 
            WHERE table_name = ?
            ORDER BY 
                CASE importance_level 
                    WHEN 'CRITICAL' THEN 1 
                    WHEN 'HIGH' THEN 2 
                    WHEN 'MEDIUM' THEN 3 
                    ELSE 4 
                END,
                rule_category
        """, (table_name,))
        
        business_rules = cursor.fetchall()
        for rule in business_rules:
            metadata["business_rules"].append({
                "category": rule[0],
                "description": rule[1],
                "sql_example": rule[2],
                "importance": rule[3]
            })
        
        metadata["has_metadata"] = True
        conn.close()
        
        print(f"✅ 成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则")
        return metadata
        
    except Exception as e:
        print(f"⚠️  获取元数据失败: {e}")
        return {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }

def generate_enhanced_prompt(query: str, metadata: Dict[str, Any]) -> str:
    """生成包含元数据的增强Prompt"""
    
    # 构建元数据增强部分
    metadata_str = ""
    if metadata.get("has_metadata"):
        metadata_str = "\n### 🎯 财务业务元数据 (重要!)\n\n"
        
        # 表描述
        if metadata.get("table_description"):
            table_desc = metadata["table_description"]
            metadata_str += f"**表说明**: {table_desc['description']}\n"
            metadata_str += f"**业务用途**: {table_desc['business_purpose']}\n"
            metadata_str += f"**数据规模**: {table_desc['data_scale']}\n\n"
        
        # 关键业务规则
        if metadata.get("business_rules"):
            metadata_str += "### ⚠️ 关键业务规则 (必须遵守!):\n\n"
            for rule in metadata["business_rules"]:
                if rule["importance"] in ["CRITICAL", "HIGH"]:
                    metadata_str += f"**【{rule['importance']}】{rule['category']}**:\n"
                    metadata_str += f"- {rule['description']}\n"
                    if rule["sql_example"]:
                        metadata_str += f"- 示例: `{rule['sql_example']}`\n"
                    metadata_str += "\n"
        
        # 关键字段说明
        if metadata.get("column_descriptions"):
            metadata_str += "### 📋 关键字段说明:\n\n"
            
            # 重点显示金额字段
            amount_fields = []
            for col in metadata["column_descriptions"]:
                col_name = col["column_name"]
                if "amount" in col_name or col_name == "balance":
                    chinese_name = col["chinese_name"]
                    ai_points = col["ai_understanding_points"]
                    amount_fields.append(f"- **{col_name}** ({chinese_name}): {ai_points}")
            
            if amount_fields:
                metadata_str += "**💰 金额字段 (关键)**:\n"
                for field in amount_fields:
                    metadata_str += f"{field}\n"
                metadata_str += "\n"
    
    # 构建完整prompt
    prompt = f"""
你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。

### 📊 数据库表: financial_data (财务辅助科目余额表)

{metadata_str}

### 🎯 用户查询:
{query}

### 📝 生成要求:

1. **严格遵循业务规则**: 特别注意不同科目类型使用不同的金额字段
2. **正确的字段选择**: 
   - 资产负债类科目 (1xxx, 2xxx, 3xxx) → 使用 `balance` 字段
   - 收入类科目 (60xx) → 使用 `credit_amount` 字段  
   - 成本费用类科目 (64xx, 66xx) → 使用 `debit_amount` 字段
3. **数据类型处理**: balance字段为TEXT类型，需要 `CAST(balance AS REAL)` 转换
4. **科目识别**: 根据科目编号自动识别科目类别并选择正确字段
5. **标准SQL语法**: 生成有效的SELECT语句

请生成准确的SQL查询:
"""
    
    return prompt

def test_query_scenarios():
    """测试不同查询场景的prompt生成"""
    
    # 获取元数据
    db_path = "fin_data.db"
    metadata = get_financial_metadata(db_path)
    
    if not metadata.get("has_metadata"):
        print("❌ 无法获取元数据，测试终止")
        return
    
    # 测试查询场景
    test_queries = [
        "查询2024年9月的收入情况",
        "显示2024年9月各单位的资产余额",
        "分析2024年9月的管理费用支出",
        "统计银行存款的余额"
    ]
    
    print("\n" + "=" * 80)
    print("🧪 测试增强Prompt生成")
    print("=" * 80)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. 测试查询: {query}")
        print("-" * 60)
        
        prompt = generate_enhanced_prompt(query, metadata)
        
        # 显示prompt的关键部分
        lines = prompt.split('\n')
        in_metadata_section = False
        metadata_lines = []
        
        for line in lines:
            if "财务业务元数据" in line:
                in_metadata_section = True
            elif "用户查询:" in line:
                in_metadata_section = False
            elif in_metadata_section and line.strip():
                metadata_lines.append(line)
        
        print("📋 生成的元数据部分:")
        for line in metadata_lines[:10]:  # 显示前10行
            print(line)
        if len(metadata_lines) > 10:
            print(f"... 还有{len(metadata_lines)-10}行")
        
        print(f"\n📊 Prompt总长度: {len(prompt)} 字符")

def demonstrate_integration_benefits():
    """演示集成元数据系统的好处"""
    
    print("\n" + "=" * 80)
    print("🎯 集成效果对比")
    print("=" * 80)
    
    # 获取元数据
    db_path = "fin_data.db"
    metadata = get_financial_metadata(db_path)
    
    query = "查询2024年9月的收入情况"
    
    # 传统prompt (无元数据)
    traditional_prompt = f"""
你是一名SQL专家，请将自然语言转换为SQL查询。

数据库表: financial_data
用户查询: {query}

请生成SQL查询:
"""
    
    # 增强prompt (有元数据)
    enhanced_prompt = generate_enhanced_prompt(query, metadata)
    
    print(f"📊 传统Prompt长度: {len(traditional_prompt)} 字符")
    print(f"🚀 增强Prompt长度: {len(enhanced_prompt)} 字符")
    print(f"📈 增强比例: {len(enhanced_prompt)/len(traditional_prompt):.1f}x")
    
    print(f"\n❌ 传统方式可能生成的错误SQL:")
    print("SELECT SUM(balance) FROM financial_data WHERE account_name LIKE '%收入%'")
    
    print(f"\n✅ 增强方式应该生成的正确SQL:")
    print("SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%'")
    
    print(f"\n🎯 关键改进:")
    print("1. 正确识别收入类科目使用credit_amount字段")
    print("2. 使用科目编号规律(60xx)而非模糊匹配")
    print("3. 遵循财务业务规则")

def main():
    """主函数"""
    print("🎯 增强元数据系统测试")
    print("=" * 60)
    
    # 测试元数据获取
    db_path = "fin_data.db"
    metadata = get_financial_metadata(db_path)
    
    print("\n📊 元数据加载结果:")
    print(f"表描述: {'✅' if metadata.get('table_description') else '❌'}")
    print(f"字段数量: {len(metadata.get('column_descriptions', []))}")
    print(f"业务规则数量: {len(metadata.get('business_rules', []))}")
    print(f"元数据可用: {'✅' if metadata.get('has_metadata') else '❌'}")
    
    if metadata.get("has_metadata"):
        # 测试查询场景
        test_query_scenarios()
        
        # 演示集成效果
        demonstrate_integration_benefits()
        
        print("\n" + "=" * 80)
        print("🎉 测试完成！元数据系统工作正常")
        print("\n📋 下一步:")
        print("1. 修改chatdb/backend/app/services/text2sql_service.py")
        print("2. 集成get_financial_metadata()函数")
        print("3. 更新construct_prompt()函数")
        print("4. 测试实际的Text2SQL功能")
    else:
        print("\n❌ 元数据系统不可用，请检查数据库配置")

if __name__ == "__main__":
    main()
