#!/usr/bin/env python3
"""
快速回滚脚本
如果代码更新出现问题，可以快速回滚到数据库优化前的状态
"""

import sqlite3
import shutil
from datetime import datetime
from pathlib import Path

class QuickRollback:
    """快速回滚工具"""
    
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def rollback_all(self):
        """回滚所有更改"""
        print("🔄 开始快速回滚...")
        print("=" * 50)
        
        try:
            # 1. 回滚数据库
            self._rollback_databases()
            
            # 2. 回滚代码（如果有备份）
            self._rollback_code()
            
            print(f"\n✅ 回滚完成！")
            print(f"💡 提示: 系统已恢复到数据库优化前的状态")
            return True
            
        except Exception as e:
            print(f"\n❌ 回滚过程中出现错误: {e}")
            return False
    
    def _rollback_databases(self):
        """回滚数据库"""
        print(f"\n📁 回滚数据库...")
        
        # 查找最新的数据库备份
        backup_dirs = list(Path("database_backups").glob("*"))
        if not backup_dirs:
            print(f"  ⚠️ 未找到数据库备份")
            return
        
        # 使用最新的备份
        latest_backup = max(backup_dirs, key=lambda d: d.stat().st_mtime)
        print(f"  📄 使用备份: {latest_backup}")
        
        # 恢复数据库文件
        backup_files = [
            ('resource.db.backup', 'resource.db'),
            ('fin_data.db.backup', 'fin_data.db')
        ]
        
        for backup_file, target_file in backup_files:
            backup_path = latest_backup / backup_file
            if backup_path.exists():
                shutil.copy2(backup_path, target_file)
                print(f"  ✅ 恢复: {backup_file} → {target_file}")
            else:
                print(f"  ⚠️ 备份文件不存在: {backup_path}")
    
    def _rollback_code(self):
        """回滚代码更改"""
        print(f"\n📁 回滚代码更改...")
        
        # 手动恢复核心文件的表名
        core_files = [
            'chatdb/backend/app/models/metadata_models.py',
            'chatdb/backend/app/services/text2sql_utils.py',
            'chatdb/backend/app/services/text2sql_service.py',
            'chatdb/backend/app/services/enhanced_prompt_service.py'
        ]
        
        for file_path in core_files:
            if Path(file_path).exists():
                self._restore_original_table_names(file_path)
                print(f"  ✅ 恢复: {file_path}")
    
    def _restore_original_table_names(self, file_path):
        """恢复原始表名"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 恢复表名
            replacements = [
                ('meta_table_descriptions', 'table_descriptions'),
                ('meta_column_descriptions', 'column_descriptions'),
                ('meta_business_rules', 'business_rules'),
                ('biz_table_descriptions', 'table_descriptions'),
                ('biz_column_descriptions', 'column_descriptions'),
                ('biz_business_rules', 'business_rules'),
            ]
            
            for new_name, old_name in replacements:
                content = content.replace(new_name, old_name)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        except Exception as e:
            print(f"  ❌ 恢复文件失败 {file_path}: {e}")

def main():
    """主函数"""
    print("🔄 快速回滚工具")
    print(f"📅 回滚时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("⚠️ 警告: 此操作将撤销所有数据库架构优化！")
    
    # 确认操作
    confirm = input("\n是否确认执行回滚？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 回滚操作已取消")
        return 0
    
    rollback = QuickRollback()
    success = rollback.rollback_all()
    
    if success:
        print(f"\n🎉 回滚成功完成！")
    else:
        print(f"\n❌ 回滚过程中出现错误")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
