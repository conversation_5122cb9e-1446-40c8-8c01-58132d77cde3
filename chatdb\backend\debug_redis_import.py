#!/usr/bin/env python3
"""
调试Redis导入问题的脚本
"""
import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_redis_import():
    """测试基本Redis导入"""
    print("🔍 测试基本Redis导入...")
    try:
        import redis
        print(f"✅ redis模块导入成功，版本: {redis.__version__}")
        return True
    except Exception as e:
        print(f"❌ redis模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_redis_asyncio_import():
    """测试Redis asyncio导入"""
    print("\n🔍 测试Redis asyncio导入...")
    try:
        import redis.asyncio as redis
        print("✅ redis.asyncio导入成功")
        return True
    except Exception as e:
        print(f"❌ redis.asyncio导入失败: {e}")
        traceback.print_exc()
        return False

def test_redis_exceptions_import():
    """测试Redis异常导入"""
    print("\n🔍 测试Redis异常导入...")
    try:
        from redis.exceptions import ConnectionError, TimeoutError
        print("✅ Redis异常类导入成功")
        return True
    except Exception as e:
        print(f"❌ Redis异常类导入失败: {e}")
        traceback.print_exc()
        return False

def test_config_import():
    """测试配置导入"""
    print("\n🔍 测试配置导入...")
    try:
        from app.core.config import settings
        print("✅ 配置导入成功")
        print(f"   REDIS_ENABLED: {getattr(settings, 'REDIS_ENABLED', 'NOT_SET')}")
        return True
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        traceback.print_exc()
        return False

def test_redis_cache_service_import():
    """测试Redis缓存服务导入"""
    print("\n🔍 测试Redis缓存服务导入...")
    try:
        from app.services.redis_cache_service import redis_cache
        print("✅ Redis缓存服务导入成功")
        return True
    except Exception as e:
        print(f"❌ Redis缓存服务导入失败: {e}")
        traceback.print_exc()
        return False

def test_startup_import():
    """测试启动模块导入"""
    print("\n🔍 测试启动模块导入...")
    try:
        from app.core.startup import initialize_redis_cache
        print("✅ 启动模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 启动模块导入失败: {e}")
        traceback.print_exc()
        return False

async def test_redis_initialization():
    """测试Redis初始化"""
    print("\n🔍 测试Redis初始化...")
    try:
        from app.core.startup import initialize_redis_cache
        result = await initialize_redis_cache()
        if result:
            print("✅ Redis初始化成功")
        else:
            print("⚠️ Redis初始化失败（但没有异常）")
        return result
    except Exception as e:
        print(f"❌ Redis初始化异常: {e}")
        traceback.print_exc()
        return False

def check_python_path():
    """检查Python路径"""
    print("\n🔍 检查Python路径...")
    print("Python路径:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    print(f"\n当前工作目录: {os.getcwd()}")
    print(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}")

def check_redis_installation():
    """检查Redis安装"""
    print("\n🔍 检查Redis安装...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "show", "redis"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Redis包信息:")
            print(result.stdout)
        else:
            print("❌ Redis包未找到")
    except Exception as e:
        print(f"❌ 检查Redis安装失败: {e}")

async def main():
    """主函数"""
    print("🚀 Redis导入问题诊断工具")
    print("=" * 60)
    
    # 检查环境
    check_python_path()
    check_redis_installation()
    
    # 测试导入
    tests = [
        test_basic_redis_import,
        test_redis_asyncio_import,
        test_redis_exceptions_import,
        test_config_import,
        test_redis_cache_service_import,
        test_startup_import,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append(False)
    
    # 测试异步初始化
    try:
        init_result = await test_redis_initialization()
        results.append(init_result)
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    test_names = [
        "基本Redis导入",
        "Redis asyncio导入", 
        "Redis异常导入",
        "配置导入",
        "Redis缓存服务导入",
        "启动模块导入",
        "Redis初始化"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！Redis应该能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查上述错误信息。")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
